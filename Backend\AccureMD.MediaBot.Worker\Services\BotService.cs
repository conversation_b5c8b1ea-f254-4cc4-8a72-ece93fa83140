using System;
using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Client;
using Microsoft.Graph.Communications.Common;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Resources;
using Microsoft.Skype.Bots.Media;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using AccureMD.MediaBot.Worker.Settings;
using AccureMD.MediaBot.Worker.Models;

namespace AccureMD.MediaBot.Worker.Services
{
    public interface IBotService : IDisposable
    {
        ICommunicationsClient Client { get; }
        ConcurrentDictionary<string, CallHandler> CallHandlers { get; }
        void Initialize();
        Task<ICall> JoinCallAsync(JoinCallBody joinCallBody);
        Task EndCallByCallLegIdAsync(string callLegId);
    }

    public class BotService : IBotService
    {
        private readonly IGraphLogger _logger;
        private readonly ILogger<BotService> _appLogger;
        private readonly BotConfiguration _botConfiguration;

        public ConcurrentDictionary<string, CallHandler> CallHandlers { get; } = new ConcurrentDictionary<string, CallHandler>();
        public ICommunicationsClient Client { get; private set; }

        public BotService(
            IGraphLogger logger,
            IOptions<BotConfiguration> botConfiguration,
            ILogger<BotService> appLogger)
        {
            _logger = logger;
            _appLogger = appLogger;
            _botConfiguration = botConfiguration.Value;
        }

        public void Initialize()
        {
            var name = GetType().Assembly.GetName().Name;
            _logger.Info($"Bot Initialize starting. CallControlBaseUrl={_botConfiguration.CallControlBaseUrl}, MediaFQDN={_botConfiguration.MediaServiceFQDN}, Port={_botConfiguration.CallSignalingPort}");
            var builder = new CommunicationsClientBuilder(name, _botConfiguration.AadAppId, _logger);
            var authProvider = new AuthenticationProvider(name, _botConfiguration.AadAppId, _botConfiguration.AadAppSecret, _logger);
            builder.SetAuthenticationProvider(authProvider);
            // Ensure notification URL has no explicit :443 (Graph expects host:443 implicit)
            var notifyUrl = new UriBuilder(_botConfiguration.CallControlBaseUrl) { Port = -1 }.Uri;
            _logger.Info($"Setting NotificationUrl={notifyUrl}");
            builder.SetNotificationUrl(notifyUrl);
            builder.SetMediaPlatformSettings(_botConfiguration.MediaPlatformSettings);
            builder.SetServiceBaseUrl(_botConfiguration.PlaceCallEndpointUrl);

            this.Client = builder.Build();
            this.Client.Calls().OnIncoming += this.CallsOnIncoming;
            this.Client.Calls().OnUpdated += this.CallsOnUpdated;
            _logger.Info("Bot Initialize complete; Communications client built and events wired.");
        }

        public void Dispose()
        {
            this.Client?.Dispose();
        }

        public async Task<ICall> JoinCallAsync(JoinCallBody joinCallBody)
        {
            var scenarioId = Guid.NewGuid();
            _logger.Info($"JoinCallAsync: scenarioId={scenarioId}, displayName='{joinCallBody.DisplayName}', url length={joinCallBody.JoinURL?.Length}");

            var (chatInfo, meetingInfo) = ParseJoinURL(joinCallBody.JoinURL);
            var tenantId = (meetingInfo as OrganizerMeetingInfo).Organizer.GetPrimaryIdentity().GetTenantId();
            _logger.Info($"Parsed JoinURL: tenantId={tenantId}, thread={chatInfo.ThreadId}, message={chatInfo.MessageId}");

            var mediaSession = this.CreateLocalMediaSession();
            _logger.Info("Local media session created; constructing Graph payload...");

            var joinParams = new JoinMeetingParameters(chatInfo, meetingInfo, mediaSession)
            {
                TenantId = tenantId,
            };

            if (!string.IsNullOrWhiteSpace(joinCallBody.DisplayName))
            {
                joinParams.GuestIdentity = new Identity
                {
                    Id = Guid.NewGuid().ToString(),
                    DisplayName = joinCallBody.DisplayName,
                };
            }

            // Log the full outgoing payload details
            try
            {
                var payloadLog = new StringBuilder()
                    .AppendLine("Outgoing Graph AddAsync payload:")
                    .AppendLine($"  TenantId: {joinParams.TenantId}")
                    .AppendLine($"  ChatInfo.ThreadId: {joinParams.ChatInfo?.ThreadId}")
                    .AppendLine($"  ChatInfo.MessageId: {joinParams.ChatInfo?.MessageId}")
                    .AppendLine($"  ChatInfo.ReplyChainMessageId: {joinParams.ChatInfo?.ReplyChainMessageId}")
                    .AppendLine($"  MeetingInfo: {joinParams.MeetingInfo?.GetType().Name}")
                    .AppendLine($"  GuestIdentity.DisplayName: {joinParams.GuestIdentity?.DisplayName}")
                    .AppendLine($"  Media: Audio=Recvonly, Video=Recvonly + Vbss");
                _logger.Info(payloadLog.ToString());
            }
            catch { }

            _logger.Info("Calling Graph: Calls().AddAsync(joinParams, scenarioId)...");
            var call = await this.Client.Calls().AddAsync(joinParams, scenarioId).ConfigureAwait(false);
            call.GraphLogger.Info($"Call creation complete: {call.Id}");
            return call;
        }

        private ILocalMediaSession CreateLocalMediaSession(Guid mediaSessionId = default)
        {
            var audioSettings = new AudioSocketSettings
            {
                StreamDirections = StreamDirection.Recvonly, // only receive mixed audio
                SupportedAudioFormat = AudioFormat.Pcm16K,
                ReceiveUnmixedMeetingAudio = false
            };

            return this.Client.CreateMediaSession(
                audioSettings,
                new[] { new VideoSocketSettings { StreamDirections = StreamDirection.Recvonly, ReceiveColorFormat = VideoColorFormat.NV12 } },
                new VideoSocketSettings { StreamDirections = StreamDirection.Recvonly, MediaType = MediaType.Vbss, ReceiveColorFormat = VideoColorFormat.NV12 },
                mediaSessionId: mediaSessionId);
        }

        private void CallsOnIncoming(ICallCollection sender, CollectionEventArgs<ICall> args)
        {
            foreach (var call in args.AddedResources)
            {
                IMediaSession mediaSession = Guid.TryParse(call.Id, out Guid callId)
                    ? this.CreateLocalMediaSession(callId)
                    : this.CreateLocalMediaSession();
                var _ = call?.AnswerAsync(mediaSession).ContinueWith(t =>
                {
                    if (t.IsFaulted)
                    {
                        call.GraphLogger.Error($"Error answering call {call.Id}: {t.Exception?.GetBaseException().Message}");
                    }
                });
            }
        }

        private void CallsOnUpdated(ICallCollection sender, CollectionEventArgs<ICall> args)
        {
            foreach (var call in args.AddedResources)
            {
                var handler = new CallHandler(call, _botConfiguration);
                this.CallHandlers[call.Id] = handler;
            }

            foreach (var call in args.RemovedResources)
            {
                if (this.CallHandlers.TryRemove(call.Id, out CallHandler handler))
                {
                    handler.Dispose();
                }
            }
        }

        private (ChatInfo, MeetingInfo) ParseJoinURL(string joinURL)
        {
            if (string.IsNullOrEmpty(joinURL))
            {
                throw new ArgumentException($"Join URL cannot be null or empty: {joinURL}", nameof(joinURL));
            }

            var decodedURL = System.Net.WebUtility.UrlDecode(joinURL);
            var regex = new Regex("https://teams\\.microsoft\\.com.*/(?<thread>[^/]+)/(?<message>[^/]+)\\?context=(?<context>{.*})");
            var match = regex.Match(decodedURL);
            if (!match.Success)
            {
                throw new ArgumentException($"Join URL cannot be parsed: {joinURL}", nameof(joinURL));
            }

            using (var stream = new System.IO.MemoryStream(Encoding.UTF8.GetBytes(match.Groups["context"].Value)))
            {
                var ctxt = (Meeting)new System.Runtime.Serialization.Json.DataContractJsonSerializer(typeof(Meeting)).ReadObject(stream);
                if (string.IsNullOrEmpty(ctxt.Tid))
                {
                    throw new ArgumentException("Join URL is invalid: missing Tid", nameof(joinURL));
                }

                var chatInfo = new ChatInfo
                {
                    ThreadId = match.Groups["thread"].Value,
                    MessageId = match.Groups["message"].Value,
                    ReplyChainMessageId = ctxt.MessageId,
                };

                var meetingInfo = new OrganizerMeetingInfo
                {
                    Organizer = new IdentitySet
                    {
                        User = new Identity { Id = ctxt.Oid },
                    },
                };
                meetingInfo.Organizer.User.SetTenantId(ctxt.Tid);

                return (chatInfo, meetingInfo);
            }
            }


        public async Task EndCallByCallLegIdAsync(string callLegId)
        {
            if (string.IsNullOrWhiteSpace(callLegId))
            {
                throw new ArgumentException("callLegId is required", nameof(callLegId));
            }

            try
            {
                if (this.CallHandlers.TryGetValue(callLegId, out var handler))
                {
                    await handler.Call.DeleteAsync().ConfigureAwait(false);
                }
                else
                {
                    // If the handler isn't present (race), attempt SDK removal anyway
                    this.Client.Calls().TryForceRemove(callLegId, out ICall _);
                }
            }
            catch (Exception)
            {
                // Ensure the call is removed from SDK state even if DeleteAsync fails
                this.Client.Calls().TryForceRemove(callLegId, out ICall _);
            }
        }
    }
}

