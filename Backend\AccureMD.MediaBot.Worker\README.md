# AccureMD.MediaBot.Worker (PsiBot-style)

This service reimplements the MediaBot worker following the Microsoft PsiBot sample patterns, focusing on API surface, configuration, and integration compatibility with the existing Backend.

Key endpoints
- POST /api/calling/join — accepts { "JoinURL": "<teams-join-url>", "DisplayName": "AccureMD Media Bot" }
- GET /api/health — health probe

Configuration (appsettings.json)
- BotConfiguration: mirrors PsiBot.Service settings (BotName, AadAppId, AadAppSecret, ServiceCname, MediaServiceFQDN, ServiceDnsName, CertificateThumbprint, InstancePublicPort, CallSignalingPort, InstanceInternalPort, PlaceCallEndpointUrl, PsiStoreDirectory)

Integration
- Backend forwards join via ExternalMediaServiceClient to /api/calling/join with the PsiBot-compatible payload.
- No media SDK is started in this build-only implementation; BotService logs intent and returns generated IDs.

Next steps to enable real media
- Port PsiBot BotService.Call creation, LocalMediaSession, and BotMediaStream using Microsoft Graph Communications SDK and Microsoft.Skype.Bots.Media. This requires Windows VM, TLS cert binding, and UDP 8445.
- Map PsiBot CallHandler and media event wiring. Maintain route compatibility.

