using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph.Communications.Common.Telemetry;

namespace AccureMD.MediaBot.Worker.Services.Asr
{
    public class AsrClient
    {
        private readonly string _endpoint;
        private readonly string _token;
        private readonly string _model;
        private readonly HttpClient _client;
        private readonly IGraphLogger _logger;

        public AsrClient(string endpoint, string token, string model, IGraphLogger logger)
        {
            // Use exactly the endpoint provided via configuration (no normalization)
            _endpoint = endpoint;
            _token = token;
            _model = model;
            _logger = logger;
            _client = new HttpClient();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            if (!string.IsNullOrWhiteSpace(_token))
            {
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
            }
        }

        public async Task<string?> TranscribeAsync(byte[] wavBytes, CancellationToken ct)
        {
            // Goal: return ONLY the literal transcription without any model responses
            for (int attempt = 0; attempt < 3; attempt++)
            {
                try
                {
                    using var form = new MultipartFormDataContent();
                    var fileContent = new ByteArrayContent(wavBytes);
                    fileContent.Headers.ContentType = new MediaTypeHeaderValue("audio/wav");
                    form.Add(fileContent, "file", "chunk.wav");
                    form.Add(new StringContent(_model, Encoding.UTF8), "model");
                    form.Add(new StringContent("0"), "temperature");
                    form.Add(new StringContent("en"), "language");

                    var resp = await _client.PostAsync(_endpoint, form, ct).ConfigureAwait(false);
                    var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                    if (!resp.IsSuccessStatusCode)
                    {
                        _logger.Warn($"ASR call failed attempt {attempt + 1} {(int)resp.StatusCode}: {body}");
                        await Task.Delay(200 * (attempt + 1), ct).ConfigureAwait(false);
                        continue;
                    }

                    // Parse strictly as JSON and only accept a top-level "text" field
                    try
                    {
                        using var doc = JsonDocument.Parse(body);
                        var root = doc.RootElement;
                        // If we accidentally hit a chat/completions endpoint, it often contains a 'choices' array; reject it
                        if (root.TryGetProperty("choices", out _))
                        {
                            _logger.Warn("ASR response looks like chat/completions JSON; rejecting to avoid contamination.");
                            return null;
                        }
                        if (root.TryGetProperty("text", out var textProp) && textProp.ValueKind == JsonValueKind.String)
                        {
                            var text = textProp.GetString() ?? string.Empty;
                            return text;
                        }
                    }
                    catch (JsonException)
                    {
                        // Non-JSON response is not a valid ASR payload for this endpoint; ignore to avoid contamination
                        return null;
                    }

                    return null;
                }
                catch (Exception ex) when (attempt < 2)
                {
                    _logger.Warn($"ASR request error attempt {attempt + 1}: {ex.Message}");
                    await Task.Delay(200 * (attempt + 1), ct).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "ASR request error");
                    return null;
                }
            }
            return null;
        }


    }
}
