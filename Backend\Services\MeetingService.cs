using Microsoft.EntityFrameworkCore;
using AccureMD.TeamsBot.Models;
using AccureMD.TeamsBot.Data;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;

namespace AccureMD.TeamsBot.Services;

public class MeetingService
{
    private readonly AuthenticationService _authService;
    private readonly ExternalMediaServiceClient _mediaClient;
    private readonly RecordingService _recordingService;
    private readonly TranscriptionService _transcriptionService;
    private readonly StorageService _storageService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<MeetingService> _logger;
    private static readonly ConcurrentDictionary<string, MeetingModel> _activeMeetings = new(); // Persist across requests
    private static readonly ConcurrentDictionary<string, string> _callIdToMeetingId = new(); // Graph CallId -> MeetingId
    private readonly CallStateService _callState;

    public MeetingService(
        AuthenticationService authService,
        ExternalMediaServiceClient mediaClient,
        RecordingService recordingService,
        TranscriptionService transcriptionService,
        StorageService storageService,
        ApplicationDbContext dbContext,
        ILogger<MeetingService> logger,
        CallStateService callState)
    {
        _authService = authService;
        _mediaClient = mediaClient;
        _recordingService = recordingService;
        _transcriptionService = transcriptionService;
        _storageService = storageService;
        _dbContext = dbContext;
        _logger = logger;
        _callState = callState;
    }

    public async Task<MeetingResponse> JoinMeetingAsGuestAsync(string meetingUrl, string userId)
    {
        try
        {
            _logger.LogInformation($"Attempting to join meeting: {meetingUrl} for user: {userId}");

            // Extract meeting ID from URL
            var meetingId = ExtractMeetingIdFromUrl(meetingUrl);
            if (string.IsNullOrEmpty(meetingId))
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Invalid meeting URL format"
                };
            }

            // Optional: Check user auth status, but do not block join; we use app-only Graph join
            var userAuth = await _authService.GetAuthenticationStatusAsync(userId);
            if (!userAuth.IsAuthenticated)
            {
                _logger.LogWarning("User {UserId} is not authenticated; proceeding with app-only Graph join as guest.", userId);
            }

            // Create a meeting object to track
            var meeting = new MeetingModel
            {
                Id = meetingId,
                MeetingUrl = meetingUrl,
                Title = "AccureMD Recording Session",
                StartTime = DateTime.UtcNow,
                Status = "Active",
                OrganizerId = userId,
                IsRecording = false,
                IsTranscribing = false
            };

            // Store the active meeting in cache and database
            _activeMeetings[meetingId] = meeting;
            await _storageService.SaveRecordingMetadataAsync(meeting);

            // Delegate media join to external Media Service
            var tid = ExtractTenantIdFromUrl(meetingUrl);
            var joinResult = await _mediaClient.JoinMeetingAsync(
                meetingUrl,
                tid,
                displayName: "AccureMD Media Bot",
                userAccessToken: userAuth?.AccessToken);
            if (!joinResult.Success || string.IsNullOrWhiteSpace(joinResult.CallId))
            {
                _logger.LogError("Media Service join failed: {Message}", joinResult.Message);
                return new MeetingResponse
                {
                    Success = false,
                    Message = $"Failed to join meeting via Media Service: {joinResult.Message}"
                };
            }

            meeting.GraphCallId = joinResult.CallId!;
            _callIdToMeetingId[joinResult.CallId!] = meetingId;

            // Only add bot to participants after we have a call id
            meeting.Participants.Add("AccureMD Bot");

            _logger.LogInformation($"Successfully initiated join for meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Join initiated. Awaiting Graph callback.",
                MeetingId = meetingId,
                JoinUrl = meetingUrl
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to join meeting: {meetingUrl}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<MeetingResponse> StartRecordingAsync(string meetingId)
    {
        try
        {
            if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                // Fallback to database to tolerate multi-instance routing or app restarts
                var dbMeeting = await _dbContext.Meetings.FirstOrDefaultAsync(m => m.Id == meetingId);
                if (dbMeeting != null)
                {
                    _activeMeetings[meetingId] = dbMeeting;
                    meeting = dbMeeting;
                }
            }

            if (meeting == null)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Meeting not found or not active"
                };
            }

            if (meeting.IsRecording)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Recording is already active"
                };
            }

            // Start recording
            var recordingResult = await _recordingService.StartRecordingAsync(meetingId, meeting.MeetingUrl ?? "");
            if (!recordingResult.Success)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = $"Failed to start recording: {recordingResult.Message}"
                };
            }

            // Start transcription
            var transcriptionResult = await _transcriptionService.StartLiveTranscriptionAsync(meetingId);
            if (!transcriptionResult.Success)
            {
                _logger.LogWarning($"Transcription failed to start for meeting {meetingId}: {transcriptionResult.Message}");
            }

            // Update meeting status
            meeting.IsRecording = true;
            meeting.IsTranscribing = transcriptionResult.Success;
            meeting.RecordingPath = recordingResult.RecordingPath ?? "";

            _logger.LogInformation($"Started recording for meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Recording started successfully",
                MeetingId = meetingId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to start recording for meeting {meetingId}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

        public async Task<MeetingResponse> PauseTranscriptionAsync(string meetingId)
        {
            try
            {
                if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
                {
                    var dbMeeting = await _dbContext.Meetings.FirstOrDefaultAsync(m => m.Id == meetingId);
                    if (dbMeeting != null) { _activeMeetings[meetingId] = dbMeeting; meeting = dbMeeting; }
                }
                if (meeting == null || string.IsNullOrWhiteSpace(meeting.GraphCallId))
                {
                    return new MeetingResponse { Success = false, Message = "Meeting not found or no active call" };
                }
                var result = await _mediaClient.PauseAsrAsync(meeting.GraphCallId);
                if (!result.Success)
                {
                    return new MeetingResponse { Success = false, Message = result.Message };
                }
                meeting.IsTranscribing = false;
                return new MeetingResponse { Success = true, Message = "Transcription paused", MeetingId = meetingId };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to pause transcription for meeting {MeetingId}", meetingId);
                return new MeetingResponse { Success = false, Message = ex.Message };
            }
        }

        public async Task<MeetingResponse> ResumeTranscriptionAsync(string meetingId)
        {
            try
            {
                if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
                {
                    var dbMeeting = await _dbContext.Meetings.FirstOrDefaultAsync(m => m.Id == meetingId);
                    if (dbMeeting != null) { _activeMeetings[meetingId] = dbMeeting; meeting = dbMeeting; }
                }
                if (meeting == null || string.IsNullOrWhiteSpace(meeting.GraphCallId))
                {
                    return new MeetingResponse { Success = false, Message = "Meeting not found or no active call" };
                }
                var result = await _mediaClient.ResumeAsrAsync(meeting.GraphCallId);
                if (!result.Success)
                {
                    return new MeetingResponse { Success = false, Message = result.Message };
                }
                meeting.IsTranscribing = true;
                return new MeetingResponse { Success = true, Message = "Transcription resumed", MeetingId = meetingId };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resume transcription for meeting {MeetingId}", meetingId);
                return new MeetingResponse { Success = false, Message = ex.Message };
            }
        }


    public async Task<MeetingResponse> StopRecordingAsync(string meetingId)
    {
        try
        {
            if (!_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "Meeting not found or not active"
                };
            }

            if (!meeting.IsRecording)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = "No active recording found"
                };
            }

            // Stop recording
            var recordingResult = await _recordingService.StopRecordingAsync(meetingId);
            if (!recordingResult.Success)
            {
                return new MeetingResponse
                {
                    Success = false,
                    Message = $"Failed to stop recording: {recordingResult.Message}"
                };
            }

            // Stop transcription
            var transcriptionResult = await _transcriptionService.StopLiveTranscriptionAsync(meetingId);

            // Update meeting status
            meeting.IsRecording = false;
            meeting.IsTranscribing = false;
            meeting.EndTime = DateTime.UtcNow;
            meeting.Status = "Completed";
            meeting.TranscriptPath = transcriptionResult.TranscriptPath ?? "";

            _logger.LogInformation($"Stopped recording for meeting {meetingId}");

            return new MeetingResponse
            {
                Success = true,
                Message = "Recording stopped and saved successfully",
                MeetingId = meetingId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to stop recording for meeting {meetingId}");
            return new MeetingResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public async Task<string> GetBotStatusAsync()
    {
        await Task.CompletedTask;

        var activeCount = _activeMeetings.Count(m => m.Value.Status == "Active");
        var recordingCount = _activeMeetings.Count(m => m.Value.IsRecording);

        return $"Online | Active Meetings: {activeCount} | Recording: {recordingCount}";
    }

    // Map from Graph callId (MediaBot Worker) to our meetingId
    public string? FindMeetingIdByCallId(string callId)
    {
        if (_callIdToMeetingId.TryGetValue(callId, out var mid)) return mid;
        // Fallback: best-effort heuristic: match active meeting where GraphCallId matches
        var kv = _activeMeetings.FirstOrDefault(kv => string.Equals(kv.Value.GraphCallId, callId, StringComparison.OrdinalIgnoreCase));
        return kv.Equals(default(KeyValuePair<string, MeetingModel>)) ? null : kv.Key;
    }

    public async Task<MeetingModel?> GetMeetingAsync(string meetingId)
    {
        await Task.CompletedTask;
        _activeMeetings.TryGetValue(meetingId, out var meeting);
        return meeting;
    }

    public async Task<List<MeetingModel>> GetActiveMeetingsAsync(string userId)
    {
        await Task.CompletedTask;
        return _activeMeetings.Values
            .Where(m => m.OrganizerId == userId && m.Status == "Active")
            .ToList();
    }

    private string ExtractMeetingIdFromUrl(string meetingUrl)
    {
        try
        {
            // Extract meeting ID from Teams meeting URL
            // Teams URLs typically contain the meeting ID in various formats
            var patterns = new[]
            {
                @"meetup-join/([a-zA-Z0-9\-%_@\.]+)",
                @"conversations/([a-zA-Z0-9\-_]+)",
                @"thread\.v2/([a-zA-Z0-9\-_]+)",
                @"meetingId=([a-zA-Z0-9\-_]+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(meetingUrl, pattern);
                if (match.Success)
                {
                    // Decode if URL-encoded
                    var val = match.Groups[1].Value;
                    try { return Uri.UnescapeDataString(val); } catch { return val; }
                }
            }

            // If no pattern matches, generate a unique ID based on the URL
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(meetingUrl))
                .Replace("+", "-").Replace("/", "_").Replace("=", "")[..16];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract meeting ID from URL: {MeetingUrl}", meetingUrl);
            return Guid.NewGuid().ToString("N")[..16];
        }
    }

    private static string? ExtractTenantIdFromUrl(string meetingUrl)
    {
        var match = Regex.Match(meetingUrl, "[?&]context=([^&]+)");
        if (!match.Success) return null;
        var encoded = match.Groups[1].Value;
        try
        {
            var json = Uri.UnescapeDataString(encoded);
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("Tid", out var tid))
                return tid.GetString();
        }
        catch { }
        return null;
    }

    private async Task SimulateJoinMeetingAsync(MeetingModel meeting)
    {
        // This method simulates joining a meeting
        // In a real implementation, this would use the Microsoft Graph API
        // or Teams SDK to join the meeting as a bot participant

        await Task.Delay(1000); // Simulate network delay

        _logger.LogInformation($"Bot joined meeting: {meeting.Id}");

        // Add bot as a participant
        meeting.Participants.Add("AccureMD Bot");
    }

    public async Task LeaveMeetingAsync(string meetingId)
    {
        try
        {
            if (_activeMeetings.TryGetValue(meetingId, out var meeting))
            {
                // Stop any active recording/transcription
                if (meeting.IsRecording)
                {
                    await StopRecordingAsync(meetingId);
                }

                // Ask MediaBot Worker to end the call if we have a callId
                if (!string.IsNullOrWhiteSpace(meeting.GraphCallId))
                {
                    try
                    {
                        var res = await _mediaClient.EndCallAsync(meeting.GraphCallId);
                        if (!res.Success)
                        {
                            _logger.LogWarning("EndCallAsync failed: {Message}", res.Message);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error calling EndCallAsync for callId {CallId}", meeting.GraphCallId);
                    }
                }

                // Mark meeting as completed
                meeting.Status = "Completed";
                meeting.EndTime = DateTime.UtcNow;

                _logger.LogInformation($"Left meeting: {meetingId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to leave meeting {meetingId}");
        }
    }
}