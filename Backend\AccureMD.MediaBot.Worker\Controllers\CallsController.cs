using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using AccureMD.MediaBot.Worker.Services;
using System;
using System.Threading.Tasks;


namespace AccureMD.MediaBot.Worker.Controllers
{
    [ApiController]
    [Route("api/calls")]
    public class CallsController : ControllerBase
    {
        private readonly IBotService _botService;
        private readonly ILogger<CallsController> _logger;

        public CallsController(IBotService botService, ILogger<CallsController> logger)
        {
            _botService = botService;
            _logger = logger;
        }

        [HttpDelete("{callLegId}")]
        public async Task<IActionResult> EndCallAsync(string callLegId)
        {
            if (string.IsNullOrWhiteSpace(callLegId))
            {
                return BadRequest(new { message = "callLegId is required" });
            }

            try
            {
                await _botService.EndCallByCallLegIdAsync(callLegId);
                return Ok(new { message = $"Ending call {callLegId}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending call {CallId}", callLegId);
                return StatusCode(500, new { message = ex.Message });
            }
        }
    }
}
