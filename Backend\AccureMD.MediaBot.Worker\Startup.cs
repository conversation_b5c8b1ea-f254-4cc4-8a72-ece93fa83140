using AccureMD.MediaBot.Worker.Services;
using AccureMD.MediaBot.Worker.Settings;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Communications.Common.Telemetry;

namespace AccureMD.MediaBot.Worker
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_2_1);

            services.AddSingleton<IGraphLogger, GraphLogger>(_ => new GraphLogger("AccureMD.MediaBot", redirectToTrace: true));
            services.Configure<BotConfiguration>(Configuration.GetSection(nameof(BotConfiguration)));
            services.PostConfigure<BotConfiguration>(config => config.Initialize());
            services.AddSingleton<IBotService, BotService>(provider =>
            {
                var bot = new BotService(
                    provider.GetRequiredService<IGraphLogger>(),
                    provider.GetRequiredService<IOptions<BotConfiguration>>(),
                    provider.GetRequiredService<ILogger<BotService>>());
                bot.Initialize();
                return bot;
            });
        }

        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseMvc();
        }
    }
}

