using Microsoft.AspNetCore.Mvc;

namespace AccureMD.TeamsBot.Controllers
{
    [ApiController]
    public class HomeController : ControllerBase
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        [HttpGet("/")]
        public IActionResult Index()
        {
            _logger.LogInformation("Root route accessed, redirecting to index.html");
            return Redirect("/html/index.html");
        }

        [HttpGet("/index")]
        public IActionResult IndexAlternate()
        {
            _logger.LogInformation("Index route accessed, redirecting to index.html");
            return Redirect("/html/index.html");
        }

    }
}
