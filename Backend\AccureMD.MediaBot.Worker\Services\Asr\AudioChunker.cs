using System;
using System.IO;

namespace AccureMD.MediaBot.Worker.Services.Asr
{
    // Buffers PCM16K mono into WAV chunks using simple VAD/silence-based segmentation.
    public class AudioChunker
    {
        public class ChunkResult
        {
            public byte[] Wav { get; set; } = Array.Empty<byte>();
            public bool SpeakerBoundary { get; set; } // true when cut was caused by sustained silence
            public bool HasSpeech { get; set; }      // true when the chunk contains voiced audio
        }

        private readonly int _sampleRate = 16000;
        private readonly short _channels = 1;
        private readonly short _bitsPerSample = 16;

        private readonly int _minChunkMs;
        private readonly int _maxChunkMs;
        private readonly short _silenceThreshold; // absolute PCM value threshold
        private readonly int _silenceHangMs;      // how long silence must last to cut

        private readonly MemoryStream _pcmBuffer = new MemoryStream();
        private int _consecutiveSilenceSamples = 0;

        public AudioChunker(int minChunkMs = 800, int maxChunkMs = 4000, short silenceThreshold = 500, int silenceHangMs = 300)
        {
            _minChunkMs = minChunkMs;
            _maxChunkMs = maxChunkMs;
            _silenceThreshold = silenceThreshold;
            _silenceHangMs = silenceHangMs;
        }

        public void Append(byte[] pcm)
        {
            _pcmBuffer.Write(pcm, 0, pcm.Length);
        }

        // Try to cut a chunk according to VAD rules. Returns null if not ready.
        public ChunkResult? TryGetNextChunk()
        {
            var bytes = _pcmBuffer.ToArray();
            var sampleCount = bytes.Length / 2; // 16-bit
            if (sampleCount == 0)
                return null;

            int minSamples = _sampleRate * _minChunkMs / 1000;
            int maxSamples = _sampleRate * _maxChunkMs / 1000;
            int hangSamples = _sampleRate * _silenceHangMs / 1000;

            // Require at least minSamples buffered
            if (sampleCount < minSamples)
                return null;

            // Scan last part for sustained silence and detect voiced frames in the chunk
            int startScan = Math.Max(0, sampleCount - hangSamples);
            bool sustainedSilence = true;
            for (int i = startScan; i < sampleCount; i++)
            {
                short s = BitConverter.ToInt16(bytes, i * 2);
                if (Math.Abs(s) > _silenceThreshold)
                {
                    sustainedSilence = false; break;
                }
            }

            // Also determine whether the prospective chunk contains any speech at all
            // We'll scan from 0..cutSamples later, but do a quick check now on entire buffer
            bool anySpeech = false;
            for (int i = 0; i < sampleCount; i++)
            {
                short s = BitConverter.ToInt16(bytes, i * 2);
                if (Math.Abs(s) > _silenceThreshold)
                {
                    anySpeech = true; break;
                }
            }

            bool shouldCut = sustainedSilence || sampleCount >= maxSamples;
            if (!shouldCut)
                return null;

            // Determine cut index; include a small tail overlap to avoid cutting words
            int cutSamples = sustainedSilence ? startScan : Math.Min(sampleCount, maxSamples);
            int overlapSamples = _sampleRate * 100 / 1000; // 100ms overlap
            cutSamples = Math.Max(cutSamples, minSamples);
            int cutBytes = cutSamples * 2;

            // Energy/threshold VAD across the cut window
            int voiced = 0;
            for (int i = 0; i < cutSamples; i++)
            {
                short s = BitConverter.ToInt16(bytes, i * 2);
                if (Math.Abs(s) > _silenceThreshold) voiced++;
            }
            double voicedRatio = cutSamples > 0 ? (double)voiced / cutSamples : 0.0;
            bool hasSpeech = anySpeech && voicedRatio > 0.02; // at least 2% of samples above threshold

            byte[] chunkPcm;
            if (hasSpeech)
            {
                chunkPcm = new byte[cutBytes];
                Array.Copy(bytes, 0, chunkPcm, 0, cutBytes);
            }
            else
            {
                chunkPcm = Array.Empty<byte>();
            }

            // Keep tail (with overlap) in buffer
            int keepStart = Math.Max(0, cutBytes - overlapSamples * 2);
            var remaining = new byte[bytes.Length - keepStart];
            Array.Copy(bytes, keepStart, remaining, 0, remaining.Length);
            _pcmBuffer.SetLength(0);
            _pcmBuffer.Write(remaining, 0, remaining.Length);

            if (!hasSpeech)
            {
                // Return boundary-only marker if we cut on silence
                return new ChunkResult { Wav = Array.Empty<byte>(), SpeakerBoundary = sustainedSilence, HasSpeech = false };
            }

            return new ChunkResult { Wav = WrapWav(chunkPcm), SpeakerBoundary = sustainedSilence, HasSpeech = true };
        }

        private byte[] WrapWav(byte[] pcm)
        {
            using var ms = new MemoryStream();
            using var bw = new BinaryWriter(ms);

            // WAV header
            bw.Write(System.Text.Encoding.ASCII.GetBytes("RIFF"));
            bw.Write(36 + pcm.Length);
            bw.Write(System.Text.Encoding.ASCII.GetBytes("WAVE"));
            bw.Write(System.Text.Encoding.ASCII.GetBytes("fmt "));
            bw.Write(16); // PCM
            bw.Write((short)1);
            bw.Write(_channels);
            bw.Write(_sampleRate);
            bw.Write(_sampleRate * _channels * _bitsPerSample / 8);
            bw.Write((short)(_channels * _bitsPerSample / 8));
            bw.Write(_bitsPerSample);
            bw.Write(System.Text.Encoding.ASCII.GetBytes("data"));
            bw.Write(pcm.Length);
            bw.Write(pcm);

            return ms.ToArray();
        }
    }
}

