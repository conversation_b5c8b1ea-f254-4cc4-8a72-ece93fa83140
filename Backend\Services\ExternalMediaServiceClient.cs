using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Identity.Client;

namespace AccureMD.TeamsBot.Services;

public class ExternalMediaServiceClient
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalMediaServiceClient> _logger;

    public ExternalMediaServiceClient(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ExternalMediaServiceClient> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<(bool Success, string? CallId, string Message)> JoinMeetingAsync(
        string meetingUrl,
        string? tenantId = null,
        string? displayName = null,
        string? userAccessToken = null)
    {
        try
        {
            var client = _httpClientFactory.CreateClient("MediaService");
            var url = _configuration["MediaService:JoinUrl"] ?? "api/calling/join"; // Use config for endpoint

            _logger.LogInformation("Forwarding join request to MediaBot Worker at {Url}", url);

            // Build payload to Media Worker
            var payloadJson = BuildMediaWorkerJoinPayload(
                meetingUrl: meetingUrl,
                displayName: displayName);

            _logger.LogInformation("MediaWorker payload: {Payload}", payloadJson);

            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(payloadJson, Encoding.UTF8, "application/json")
            };

            var response = await client.SendAsync(request);
            var body = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Media Service join failed. Status={Status}, Body={Body}", (int)response.StatusCode, body);
                return (false, null, $"Media Service returned error: {(int)response.StatusCode} - {body}");
            }

            _logger.LogInformation("Media Service join succeeded. Response: {Body}", body);

            using var doc = JsonDocument.Parse(body);
            var callId = doc.RootElement.TryGetProperty("callId", out var idProp) ? idProp.GetString() : null;
            var message = doc.RootElement.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : "Successfully initiated call.";

            return (true, callId, message ?? "Success");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling External Media Service");
            return (false, null, $"Internal error calling media service: {ex.Message}");
        }
    }

    private string BuildMediaWorkerJoinPayload(
        string meetingUrl,
        string? displayName)
    {
        // Build JSON payload for the new MediaBot implementation
        using var stream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(stream))
        {
            writer.WriteStartObject();
            writer.WriteString("JoinURL", meetingUrl);
            writer.WriteString("DisplayName", string.IsNullOrWhiteSpace(displayName) ? "AccureMD Media Bot" : displayName);
            writer.WriteEndObject();
        }
        stream.Position = 0;
        return Encoding.UTF8.GetString(stream.ToArray());
    }

	    public async Task<(bool Success, string Message)> EndCallAsync(string callLegId)
	    {
	        try
	        {
	            var client = _httpClientFactory.CreateClient("MediaService");
	            var url = _configuration["MediaService:EndCallUrl"] ?? $"api/calls/{callLegId}";
	            var request = new HttpRequestMessage(HttpMethod.Delete, url);
	            var response = await client.SendAsync(request);
	            var body = await response.Content.ReadAsStringAsync();
	            if (!response.IsSuccessStatusCode)
	            {
	                _logger.LogWarning("Media Service end call failed. Status={Status}, Body={Body}", (int)response.StatusCode, body);
	                return (false, $"Media Service error: {(int)response.StatusCode} - {body}");
	            }
	            return (true, "Call end requested successfully");
	        }
	        catch (Exception ex)
	        {
	            _logger.LogError(ex, "Error calling External Media Service EndCall");
	            return (false, $"Internal error calling media service: {ex.Message}");
	        }
	    }

        public async Task<(bool Success, string Message)> PauseAsrAsync(string callId)
        {
            try
            {
                var client = _httpClientFactory.CreateClient("MediaService");
                var url = _configuration["MediaService:PauseUrl"] ?? $"api/asr/{callId}/pause";
                var resp = await client.PostAsync(url, content: null);
                var body = await resp.Content.ReadAsStringAsync();
                if (!resp.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Media Service pause ASR failed. Status={Status}, Body={Body}", (int)resp.StatusCode, body);
                    return (false, $"Media Service error: {(int)resp.StatusCode} - {body}");
                }
                return (true, "ASR paused");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling Media Service Pause ASR");
                return (false, $"Internal error: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> ResumeAsrAsync(string callId)
        {
            try
            {
                var client = _httpClientFactory.CreateClient("MediaService");
                var url = _configuration["MediaService:ResumeUrl"] ?? $"api/asr/{callId}/resume";
                var resp = await client.PostAsync(url, content: null);
                var body = await resp.Content.ReadAsStringAsync();
                if (!resp.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Media Service resume ASR failed. Status={Status}, Body={Body}", (int)resp.StatusCode, body);
                    return (false, $"Media Service error: {(int)resp.StatusCode} - {body}");
                }
                return (true, "ASR resumed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling Media Service Resume ASR");
                return (false, $"Internal error: {ex.Message}");
            }
        }


}
