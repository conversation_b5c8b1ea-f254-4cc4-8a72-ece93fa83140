using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Models;
using System.Text;
using System.Linq;


namespace AccureMD.TeamsBot.Controllers;

[ApiController]
[Route("api/transcription")]
public class TranscriptionController : ControllerBase
{
    private readonly MeetingService _meetingService;
    private readonly TranscriptionService _transcriptionService;
    private readonly ILogger<TranscriptionController> _logger;

    public TranscriptionController(MeetingService meetingService, TranscriptionService transcriptionService, ILogger<TranscriptionController> logger)
    {
        _meetingService = meetingService;
        _transcriptionService = transcriptionService;
        _logger = logger;
    }

    // Payload posted from MediaBot worker per ASR result
    public class RealtimeTranscriptPayload
    {
        public string CallId { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public double Confidence { get; set; } = 0.0;
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
        public string? SpeakerName { get; set; } = "Speaker";
        public bool IsPartial { get; set; } = false;
        public bool SpeakerBoundary { get; set; } = false;
    }

    [HttpPost("realtime")]
    public async Task<IActionResult> ReceiveRealtime([FromBody] RealtimeTranscriptPayload payload)
    {
        try
        {
            if (payload == null || string.IsNullOrWhiteSpace(payload.CallId))
            {
                return BadRequest(new { message = "Missing callId" });
            }

            var meetingId = _meetingService.FindMeetingIdByCallId(payload.CallId);
            if (string.IsNullOrEmpty(meetingId))
            {
                _logger.LogWarning("Realtime transcript received for unknown callId={CallId}", payload.CallId);
                return NotFound(new { message = "Unknown callId" });
            }

            await _transcriptionService.AddRealtimeTranscriptAsync(
                meetingId,
                payload.Text ?? string.Empty,
                payload.Confidence,
                payload.Timestamp,
                payload.SpeakerName ?? "Speaker",
                payload.IsPartial,
                payload.SpeakerBoundary);

            return Ok(new { success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling realtime transcript");
            return StatusCode(500, new { message = ex.Message });
        }
        }
        // Download full plain-text transcript composed from aggregate row if present, otherwise join in-memory
        [HttpGet("{meetingId}/text")]
        public async Task<IActionResult> DownloadPlainText(string meetingId)
        {
            try
            {
                // Prefer the aggregate text row from DB
                var transcripts = await _transcriptionService.GetLiveTranscriptsAsync(meetingId);
                string text = string.Empty;
                if (transcripts != null && transcripts.Count > 0)
                {
                    // If an aggregate row exists (first per our storage convention), use its Text
                    var agg = transcripts.OrderByDescending(t => t.Timestamp).FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(agg?.Text))
                    {
                        text = agg.Text!;
                    }
                    else
                    {
                        // Fallback: join individual transcript texts
                        text = string.Join(" ", transcripts
                            .Where(t => !string.IsNullOrWhiteSpace(t.Text))
                            .Select(t => t.Text));
                    }
                }
                var bytes = Encoding.UTF8.GetBytes(text ?? string.Empty);
                return File(bytes, "text/plain; charset=utf-8", $"AccureMD_Transcript_{meetingId}.txt");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating plain-text transcript for {MeetingId}", meetingId);
                return StatusCode(500, new { message = ex.Message });
            }
        }
    }
