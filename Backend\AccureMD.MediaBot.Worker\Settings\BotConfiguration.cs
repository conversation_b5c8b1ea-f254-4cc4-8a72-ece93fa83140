using Microsoft.Skype.Bots.Media;
using System;
using System.Collections.Generic;
using System.Net;
using System.Security.Cryptography.X509Certificates;

namespace AccureMD.MediaBot.Worker.Settings
{
    public class BotConfiguration
    {
        public string BotName { get; set; } = string.Empty;
        public string ServiceDnsName { get; set; } = string.Empty;
        public string ServiceCname { get; set; } = string.Empty;
        public string CertificateThumbprint { get; set; } = string.Empty;
        public IEnumerable<string> CallControlListeningUrls { get; set; } = Array.Empty<string>();
        public Uri CallControlBaseUrl { get; set; } = new Uri("https://localhost/");
        public Uri PlaceCallEndpointUrl { get; set; } = new Uri("https://graph.microsoft.com/v1.0");
        public MediaPlatformSettings MediaPlatformSettings { get; private set; } = null!;
        public string AadAppId { get; set; } = string.Empty;
        public string AadAppSecret { get; set; } = string.Empty;
        // ASR integration
        public string AsrEndpoint { get; set; } = ""; // e.g., http://************:40015/v1/audio/transcriptions
        public string AsrToken { get; set; } = "";
        public string AsrModel { get; set; } = "mistralai/voxtral-mini-3B-2507";
        // Backend webhook to post realtime transcripts
        public string BackendRealtimeWebhook { get; set; } = "https://accuremd.eastus.cloudapp.azure.com/api/transcription/realtime";
        public int InstancePublicPort { get; set; }
        public int InstanceInternalPort { get; set; }
        public int CallSignalingPort { get; set; } = 9441;
        public string MediaServiceFQDN { get; set; } = string.Empty;
        public string PsiStoreDirectory { get; set; } = string.Empty;

        public void Initialize()
        {
            if (string.IsNullOrWhiteSpace(ServiceCname))
            {
                ServiceCname = ServiceDnsName;
            }

            X509Certificate2 defaultCertificate = this.GetCertificateFromStore();
            List<string> controlListenUris = new List<string>();
            var baseDomain = "+";

            this.CallControlBaseUrl = new Uri($"https://{this.ServiceCname}/api/calling");
            controlListenUris.Add($"https://{baseDomain}:{CallSignalingPort}/");
            controlListenUris.Add($"http://{baseDomain}:{CallSignalingPort + 1}/");
            this.CallControlListeningUrls = controlListenUris;

            this.MediaPlatformSettings = new MediaPlatformSettings()
            {
                MediaPlatformInstanceSettings = new MediaPlatformInstanceSettings()
                {
                    CertificateThumbprint = defaultCertificate.Thumbprint,
                    InstanceInternalPort = InstanceInternalPort,
                    InstancePublicIPAddress = IPAddress.Any,
                    InstancePublicPort = InstancePublicPort,
                    ServiceFqdn = this.MediaServiceFQDN
                },
                ApplicationId = this.AadAppId,
            };
        }

        public X509Certificate2 GetCertificateFromStore()
        {
            X509Store store = new X509Store(StoreName.My, StoreLocation.LocalMachine);
            store.Open(OpenFlags.ReadOnly);
            try
            {
                X509Certificate2Collection certs = store.Certificates.Find(X509FindType.FindByThumbprint, CertificateThumbprint, validOnly: false);
                if (certs.Count != 1)
                {
                    throw new Exception($"No certificate with thumbprint {CertificateThumbprint} was found in the machine store.");
                }
                return certs[0];
            }
            finally
            {
                store.Close();
            }
        }
    }
}

