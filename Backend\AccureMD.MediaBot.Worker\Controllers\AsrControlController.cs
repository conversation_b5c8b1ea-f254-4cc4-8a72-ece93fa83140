using Microsoft.AspNetCore.Mvc;
using AccureMD.MediaBot.Worker.Services;
using Microsoft.Graph.Communications.Common.Telemetry;

namespace AccureMD.MediaBot.Worker.Controllers
{
    [ApiController]
    [Route("api/asr")] 

    public class AsrControlController : ControllerBase
    {
        private readonly IBotService _botService;
        private readonly IGraphLogger _logger;

        public AsrControlController(IBotService botService, IGraphLogger logger)
        {
            _botService = botService;
            _logger = logger;
        }

        [HttpPost("{callId}/pause")]
        public IActionResult Pause(string callId)
        {
            if (string.IsNullOrWhiteSpace(callId)) return BadRequest(new { message = "callId is required" });
            if (!_botService.CallHandlers.TryGetValue(callId, out var handler))
            {
                return NotFound(new { message = "Unknown callId" });
            }
            handler.SetAsrPaused(true);
            _logger.Info($"ASR paused for call {callId}");
            return Ok(new { success = true });
        }

        [HttpPost("{callId}/resume")]
        public IActionResult Resume(string callId)
        {
            if (string.IsNullOrWhiteSpace(callId)) return BadRequest(new { message = "callId is required" });
            if (!_botService.CallHandlers.TryGetValue(callId, out var handler))
            {
                return NotFound(new { message = "Unknown callId" });
            }
            handler.SetAsrPaused(false);
            _logger.Info($"ASR resumed for call {callId}");
            return Ok(new { success = true });
        }
    }
}

