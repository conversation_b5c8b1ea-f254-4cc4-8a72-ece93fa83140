using AccureMD.MediaBot.Worker.Settings;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

namespace AccureMD.MediaBot.Worker
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .UseStartup<Startup>()
                .UseKestrel((ctx, opt) =>
                {
                    var config = new BotConfiguration();
                    ctx.Configuration.GetSection(nameof(BotConfiguration)).Bind(config);
                    config.Initialize();

                    // Load the configured certificate for HTTPS so CN/SAN matches ServiceCname
                    X509Certificate2 cert = config.GetCertificateFromStore();

                    opt.Configure()
                        .Endpoint("HTTPS", listenOptions =>
                        {
                            listenOptions.HttpsOptions.SslProtocols = SslProtocols.Tls12;
                        });

                    opt.ListenAnyIP(config.CallSignalingPort, o => o.UseHttps(cert));
                    opt.ListenAnyIP(config.CallSignalingPort + 1);
                });
    }
}

