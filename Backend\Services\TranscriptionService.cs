using AccureMD.TeamsBot.Models;
using System.Text.Json;
using System.Collections.Concurrent;

namespace AccureMD.TeamsBot.Services;

public class TranscriptionService
{
    private readonly IConfiguration _configuration;
    private readonly StorageService _storageService;
    private readonly ILogger<TranscriptionService> _logger;
    private static readonly ConcurrentDictionary<string, TranscriptionSession> _activeTranscriptions = new();

    public TranscriptionService(
        IConfiguration configuration,
        StorageService storageService,
        ILogger<TranscriptionService> logger)
    {
        _configuration = configuration;
        _storageService = storageService;
        _logger = logger;
    }

    public Task<TranscriptionResult> StartLiveTranscriptionAsync(string meetingId)
    {
        try
        {
            if (_activeTranscriptions.ContainsKey(meetingId))
            {
                return Task.FromResult(new TranscriptionResult
                {
                    Success = false,
                    Message = "Transcription already active for this meeting"
                });
            }

            var transcriptPath = GenerateTranscriptPath(meetingId);

            var session = new TranscriptionSession
            {
                MeetingId = meetingId,
                TranscriptPath = transcriptPath,
                StartTime = DateTime.UtcNow,
                IsActive = true,
                Transcripts = new List<TranscriptModel>()
            };

            // Register live transcription session (ASR handled by MediaBot Worker)
            _activeTranscriptions[meetingId] = session;

            _logger.LogInformation($"Started live transcription session for meeting {meetingId}");

            return Task.FromResult(new TranscriptionResult
            {
                Success = true,
                Message = "Live transcription started successfully",
                TranscriptPath = transcriptPath,
                MeetingId = meetingId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to start transcription for meeting {meetingId}");
            return Task.FromResult(new TranscriptionResult
            {
                Success = false,
                Message = ex.Message
            });
        }
    }

    public async Task<TranscriptionResult> StopLiveTranscriptionAsync(string meetingId)
    {
        try
        {
            if (!_activeTranscriptions.TryGetValue(meetingId, out var session))
            {
                return new TranscriptionResult
                {
                    Success = false,
                    Message = "No active transcription found for this meeting"
                };
            }

            session.EndTime = DateTime.UtcNow;
            session.IsActive = false;

            // Finalize transcription
            await FinalizeTranscription(session);

            // Save complete transcript
            //await _storageService.SaveTranscriptAsync(session);

            _activeTranscriptions.TryRemove(meetingId, out _);

            _logger.LogInformation($"Stopped transcription for meeting {meetingId}");

            return new TranscriptionResult
            {
                Success = true,
                Message = "Transcription completed and saved",
                TranscriptPath = session.TranscriptPath,
                MeetingId = meetingId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to stop transcription for meeting {meetingId}");
            return new TranscriptionResult
            {
                Success = false,
                Message = ex.Message
            };
        }
    }

    public Task<List<TranscriptModel>> GetLiveTranscriptsAsync(string meetingId)
    {
        if (_activeTranscriptions.TryGetValue(meetingId, out var session))
        {
            return Task.FromResult(session.Transcripts.ToList());
        }

        return Task.FromResult(new List<TranscriptModel>());
    }

    public async Task AddRealtimeTranscriptAsync(string meetingId, string text, double confidence, DateTimeOffset timestamp, string speakerName, bool isPartial, bool speakerBoundary = false)
    {
        if (!_activeTranscriptions.TryGetValue(meetingId, out var session))
        {
            // If no active session exists, create a lightweight one to buffer UI updates
            session = new TranscriptionSession
            {
                MeetingId = meetingId,
                TranscriptPath = GenerateTranscriptPath(meetingId),
                StartTime = DateTime.UtcNow,
                IsActive = true,
                Transcripts = new List<TranscriptModel>()
            };
            _activeTranscriptions[meetingId] = session;
        }

        var model = new TranscriptModel
        {
            MeetingId = meetingId,
            SpeakerName = speakerName,
            Text = text,
            Confidence = confidence,
            Timestamp = timestamp.UtcDateTime,
            OffsetFromStart = timestamp.UtcDateTime - session.StartTime
        };

        // Insert a visual boundary in the UI by adding an empty line with marker
        if (speakerBoundary && session.Transcripts.Count > 0)
        {
            session.Transcripts.Add(new TranscriptModel
            {
                MeetingId = meetingId,
                SpeakerName = speakerName,
                Text = string.Empty,
                Confidence = confidence,
                Timestamp = timestamp.UtcDateTime,
                OffsetFromStart = timestamp.UtcDateTime - session.StartTime,
                SpeakerBoundary = true
            });
        }

        // Only add a transcript line when there is text
        if (!string.IsNullOrWhiteSpace(model.Text))
        {
            session.Transcripts.Add(model);

            // Persist aggregate transcript: append text to a single row per meeting (Id = organizer's userId when known)
            try
            {
                // Look up organizer/userId for this meeting (may be null if not present)
                string? organizerId = null; // resolved in StorageService if null

                await _storageService.SaveOrAppendAggregateTranscriptAsync(meetingId, organizerId, text);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to upsert aggregate transcript for meeting {MeetingId}", meetingId);
            }
        }

        _logger.LogDebug("[ASR] {MeetingId} {Speaker}: {Text}", meetingId, speakerName, text);
    }

    private string GenerateTranscriptPath(string meetingId)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var filename = $"AccureMD_Transcript_{meetingId}_{timestamp}.json";
        var storagePath = _configuration["Recording:StoragePath"] ?? "./recordings";
        return Path.Combine(storagePath, filename);
    }




    private async Task FinalizeTranscription(TranscriptionSession session)
    {
        // Create final transcript summary
        var ordered = session.Transcripts.OrderBy(t => t.OffsetFromStart).ToList();
        var summary = new
        {
            MeetingId = session.MeetingId,
            StartTime = session.StartTime,
            EndTime = session.EndTime,
            Duration = session.EndTime - session.StartTime,
            TotalTranscripts = ordered.Count,
            Participants = ordered.Select(t => t.SpeakerName).Distinct().ToList(),
            Transcripts = ordered
        };

        var json = JsonSerializer.Serialize(summary, new JsonSerializerOptions { WriteIndented = true });

        // Ensure directory exists
        var directory = Path.GetDirectoryName(session.TranscriptPath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllTextAsync(session.TranscriptPath, json);

        // Persist granular rows (sanitized: no speaker identity, no confidence)
        try
        {
            var sanitized = ordered
                .Where(t => !string.IsNullOrWhiteSpace(t.Text))
                .Select(t => new TranscriptModel
                {
                    MeetingId = t.MeetingId,
                    SpeakerId = null,
                    SpeakerName = null,
                    Text = t.Text,
                    Confidence = null,
                    Timestamp = t.Timestamp,
                    OffsetFromStart = t.OffsetFromStart
                })
                .ToList();
            if (sanitized.Count > 0)
            {
                await _storageService.SaveTranscriptsAsync(sanitized);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to persist granular transcripts to DB");
        }

        // Also append the final continuous string to the single aggregate row per meeting
        try
        {
            var full = string.Join(" ", ordered.Where(t => !string.IsNullOrWhiteSpace(t.Text)).Select(t => t.Text!.Trim()));
            if (!string.IsNullOrWhiteSpace(full))
            {
                string? organizerId = null; // resolved in StorageService if null
                await _storageService.SaveOrAppendAggregateTranscriptAsync(session.MeetingId, organizerId, full);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to append aggregated transcript string to DB");
        }

        _logger.LogInformation($"Finalized transcript saved to {session.TranscriptPath}");
    }
}

public class TranscriptionSession
{
    public string MeetingId { get; set; } = string.Empty;
    public string TranscriptPath { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool IsActive { get; set; }
    public List<TranscriptModel> Transcripts { get; set; } = new();
}

public class TranscriptionResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? TranscriptPath { get; set; }
    public string? MeetingId { get; set; }
}