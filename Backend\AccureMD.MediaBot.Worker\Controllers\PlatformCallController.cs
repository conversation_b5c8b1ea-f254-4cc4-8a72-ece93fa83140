using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph.Communications.Client;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AccureMD.MediaBot.Worker.Services;

namespace AccureMD.MediaBot.Worker.Controllers
{
    [Route("api/calling")]
    public class PlatformCallController : ControllerBase
    {
        private readonly IBotService _botService;
        private readonly IGraphLogger _logger;
        private readonly ILogger<PlatformCallController> _appLogger;

        public PlatformCallController(IBotService botService, IGraphLogger logger, ILogger<PlatformCallController> appLogger)
        {
            this._botService = botService;
            this._logger = logger;
            this._appLogger = appLogger;
        }

        // SDK posts to the base notification URL ("api/calling") for incoming requests
        [HttpPost]
        [Route("")]
        public async Task<IActionResult> OnIncomingRequestAsync()
        {
            var log = $"Notification: {this.Request.Method} {this.Request.Path.Value}";
            _logger.Info(log);
            _appLogger.LogInformation(log);

            string bodyText = await ReadBodyAsync(this.Request);
            if (!string.IsNullOrEmpty(bodyText))
            {
                _logger.Info($"Incoming Graph notification body: {bodyText}");
                _appLogger.LogInformation("Incoming Graph notification body: {Body}", bodyText);
            }

            var response = await _botService.Client.ProcessNotificationAsync(ConvertHttpRequestToHttpRequestMessage(this.Request, bodyText)).ConfigureAwait(false);
            var content = response.Content == null ? null : await response.Content?.ReadAsStringAsync();
            _logger.Info($"ProcessNotificationAsync responded {response.StatusCode}; content length={(content?.Length ?? 0)}");
            _appLogger.LogInformation("ProcessNotificationAsync responded {Status}; body={Body}", response.StatusCode, content);
            return Ok(content);
        }

        // Some SDK flows use /api/calling/notification
        [HttpPost]
        [Route("notification")]
        public async Task<IActionResult> OnNotificationRequestAsync()
        {
            var log = $"Notification: {this.Request.Method} {this.Request.Path.Value}";
            _logger.Info(log);
            _appLogger.LogInformation(log);

            string bodyText = await ReadBodyAsync(this.Request);
            if (!string.IsNullOrEmpty(bodyText))
            {
                _logger.Info($"Incoming Graph notification body: {bodyText}");
                _appLogger.LogInformation("Incoming Graph notification body: {Body}", bodyText);
            }

            var response = await _botService.Client.ProcessNotificationAsync(ConvertHttpRequestToHttpRequestMessage(this.Request, bodyText)).ConfigureAwait(false);
            var content = response.Content == null ? null : await response.Content?.ReadAsStringAsync();
            _logger.Info($"ProcessNotificationAsync responded {response.StatusCode}; content length={(content?.Length ?? 0)}");
            _appLogger.LogInformation("ProcessNotificationAsync responded {Status}; body={Body}", response.StatusCode, content);
            return Ok(content);
        }

        private static async Task<string> ReadBodyAsync(HttpRequest request)
        {
            if (request.Body == null)
            {
                return string.Empty;
            }

            using (var reader = new StreamReader(request.Body, Encoding.UTF8, detectEncodingFromByteOrderMarks: false, bufferSize: 1024, leaveOpen: true))
            {
                var body = await reader.ReadToEndAsync().ConfigureAwait(false);
                // Do not attempt to reset position on non-seekable Kestrel streams
                if (request.Body.CanSeek)
                {
                    try { request.Body.Position = 0; } catch { /* ignore */ }
                }
                return body;
            }
        }

        private HttpRequestMessage ConvertHttpRequestToHttpRequestMessage(HttpRequest request, string bodyText = null)
        {
            var uri = new Uri(request.GetDisplayUrl());
            var requestMessage = new HttpRequestMessage();
            var requestMethod = request.Method;

            if (!HttpMethods.IsGet(requestMethod) && !HttpMethods.IsHead(requestMethod) && !HttpMethods.IsDelete(requestMethod) && !HttpMethods.IsTrace(requestMethod))
            {
                if (!string.IsNullOrEmpty(bodyText))
                {
                    requestMessage.Content = new StringContent(bodyText, Encoding.UTF8, "application/json");
                }
                else
                {
                    requestMessage.Content = new StreamContent(request.Body);
                }
            }

            foreach (var header in request.Headers)
            {
                if (!requestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()) && requestMessage.Content != null)
                {
                    try { requestMessage.Content.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()); } catch { /* ignore */ }
                }
            }

            requestMessage.Headers.Host = uri.Authority;
            requestMessage.RequestUri = uri;
            requestMessage.Method = new HttpMethod(request.Method);

            return requestMessage;
        }
    }
}

