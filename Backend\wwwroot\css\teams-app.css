/* Dark mode theme aligned with Microsoft Teams */
:root {
    --teams-primary: #6264A7;
    --teams-secondary: #464775;
    --teams-accent: #8B8CC7;
    --teams-background: #0F0F10;
    --teams-surface: #1F1F20;
    --teams-text: #F3F2F1;
    --teams-text-secondary: #C8C6C4;
    --success-color: #3DBE5A;
    --warning-color: #F8D22A;
    --error-color: #FF6B6B;
    --recording-color: #E83F5B;
}

body.main-app { background-color: var(--teams-background); color: var(--teams-text); }

.header { background: linear-gradient(180deg, rgba(34,34,36,0.9), rgba(34,34,36,0.7)); border-bottom: 1px solid #2a2a2c; }
.meeting-controls, .recording-controls { background: #1b1b1d; box-shadow: 0 4px 16px rgba(0,0,0,0.35); }
.transcript-panel { background: transparent; box-shadow: none; }
.input-group input[type="url"] { background: #141415; color: var(--teams-text); border-color: #333; }
.status-display, .recording-status, .transcript-container { background: #151517; border-color: #2c2c2e; }
.footer { background: #161618; border-top-color: #2a2a2c; }
.main-app-container .header { justify-content: flex-start; position: sticky; top: 0; z-index: 50; }


/* Profile button and menu */
.profile-btn { background: transparent; border: none; cursor: pointer; padding: 2px; border-radius: 50%; }
.avatar.small { width: 28px; height: 28px; font-size: 11px; }
.avatar.large { width: 48px; height: 48px; font-size: 16px; }
.profile-menu { position: absolute; right: 16px; top: 56px; background: #232326; border: 1px solid #2e2e32; border-radius: 8px; width: 280px; z-index: 1000; box-shadow: 0 8px 28px rgba(0,0,0,0.5); padding: 12px; }
.profile-header { display: flex; gap: 12px; align-items: center; border-bottom: 1px solid #2e2e32; padding-bottom: 12px; margin-bottom: 12px; }
.profile-meta small { color: var(--teams-text-secondary); }
.profile-details { display: flex; flex-direction: column; gap: 8px; }
.detail-row { display: flex; justify-content: space-between; color: var(--teams-text-secondary); }
.detail-row strong { color: var(--teams-text); }

/* Improved recording status layout */
.recording-status { gap: 16px; }
.status-left { display: flex; flex-direction: column; gap: 8px; }
.status-row { display: flex; align-items: center; gap: 8px; font-size: 14px; }
.timer { font-size: 20px; }

/* AccureMD Teams App Styles - Dark mode is default */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--teams-background);
    color: var(--teams-text);
    line-height: 1.5;
}

.loading-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.loader {
    width: 40px;
    height: 40px;
    border: 4px solid var(--teams-accent);
    border-top: 4px solid var(--teams-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.auth-container {
    text-align: center;
    padding: 32px;
    max-width: 400px;
}

.logo-large {
    width: 64px;
    height: 64px;
    margin-bottom: 24px;
}

.auth-container h1 {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--teams-primary);
}

.auth-container p {
    color: var(--teams-text-secondary);
    margin-bottom: 32px;
    font-size: 14px;
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    width: 100%;
}

.login-btn:hover {
    background-color: var(--teams-secondary);
}

.ms-icon {
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSI+PHBhdGggZD0iTTAgMGgxMHYxMEgweiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0xMSAwaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0wIDExaDEwdjEwSDB6IiBmaWxsPSIjZjlmOWY5Ii8+PHBhdGggZD0iTTExIDExaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjwvc3ZnPg==') no-repeat center;
    margin-right: 8px;
}

.main-app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--teams-surface);
    border-bottom: 1px solid #E1DFDD;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Ensure left group (avatar+name) stays on the left and right group (more menu) pushes to the far right */
.header-left { margin-right: auto; }
.header-right { margin-left: auto; }


.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}
.header-left{display:flex;align-items:center;gap:12px}.header-right{display:flex;align-items:center;gap:8px;position:relative}.more-btn{background:transparent;border:1px solid var(--teams-accent);color:var(--teams-text);padding:4px 10px;border-radius:6px;cursor:pointer;font-size:18px;line-height:1}.more-btn:hover{background:#2a2a2e}.more-menu{position:absolute;right:0;top:42px;background:#232326;border:1px solid #2e2e32;border-radius:8px;width:180px;z-index:1000;box-shadow:0 8px 28px rgba(0,0,0,.5);padding:8px}.menu-item{width:100%;text-align:left;background:transparent;border:none;color:var(--teams-text);padding:8px 10px;border-radius:6px;cursor:pointer;font-weight:600}.menu-item:hover{background:#2a2a2e}.menu-item.danger{color:var(--error-color)}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--teams-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.logout-btn {
    background: none;
    border: 1px solid var(--teams-primary);
    color: var(--teams-primary);
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.meeting-controls, .recording-controls, .transcript-panel {
    background-color: var(--teams-surface);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.meeting-controls h2, .recording-controls h2, .transcript-panel h2 {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--teams-primary);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    font-size: 14px;
    color: var(--teams-text);
}

.input-group input[type="url"] {
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
}

.input-group input[type="url"]:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.primary-btn, .record-btn, .stop-btn, .transcribe-btn {
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    margin-top: 8px;
}

.primary-btn:hover {
    background-color: var(--teams-secondary);
}

.record-btn {
    background-color: var(--recording-color);
}

.record-btn:hover {
    background-color: #A01017;
}

.stop-btn {
    background-color: var(--teams-text-secondary);
}

.stop-btn:hover {
    background-color: #323130;
}

.transcribe-btn {
    background-color: var(--success-color);
}

.transcribe-btn:hover {
    background-color: #0B5A0B;
}

.secondary-btn {
    background-color: transparent;
    color: var(--teams-primary);
    border: 1px solid var(--teams-primary);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.status-display {
    margin-top: 16px;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.offline {
    background-color: var(--teams-text-secondary);
}

.status-dot.online {
    background-color: var(--success-color);
}

.status-dot.recording {
    background-color: var(--recording-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.control-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Connection flow styling */
.connection-flow { background: var(--teams-background); border:1px dashed #333; border-radius:8px; padding:16px; margin-top:12px; display:flex; flex-direction:column; gap:10px }
.connection-flow .flow-row { display:flex; align-items:center; gap:10px; color:var(--teams-text-secondary) }
.connection-flow .flow-progress { display:flex; justify-content:center; padding-top:4px }
.connection-flow .spinner { width:22px; height:22px; border:3px solid #444; border-top-color: var(--teams-primary); border-radius:50%; animation: spin 1s linear infinite }
    .connection-flow .flow-details { margin-top: 6px; color: var(--teams-text-secondary); font-size: 13px; }

.status-dot.connecting { background-color: var(--warning-color); animation: pulse 1s infinite }
@keyframes pulse { 0%{ opacity: .5 } 50%{ opacity: 1 } 100%{ opacity: .5 } }

/* Profile modal clean layout */
.profile-details .detail-row { gap:16px }
.profile-details .detail-row span { color: var(--teams-text-secondary) }
.profile-details .detail-row strong { word-break: break-all }
.profile-header .avatar.large { flex: 0 0 auto }
.profile-meta strong { font-size:16px }


.recording-status {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.timer {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    color: var(--teams-text);
}

.recording-indicator {
    font-size: 14px;
    font-weight: 600;
}

.transcript-container {
    min-height: 240px;
    max-height: 50vh;
    overflow-y: auto;
    border: none; /* remove box */
    border-radius: 0;
    padding: 8px 4px;
    background-color: transparent;
}

/* Flat, inline transcript lines (no boxes) */
.transcript-line {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 8px;
    align-items: baseline;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255,255,255,0.05);
}
.transcript-line:last-child { border-bottom: none; }

/* Speaker name: subtle weight and brand color */
.speaker-name { color: var(--teams-primary); font-weight: 600; letter-spacing: .2px; }

/* Timestamp aligned to right and dimmed */
.timestamp { color: var(--teams-text-secondary); font-size: 12px; }
.timestamp.right { justify-self: end; }


/* Smooth appearance for new lines */
.fade-in { animation: fadeIn .18s ease-out both; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(2px);} to { opacity: 1; transform: translateY(0);} }

/* Readable running text */
/* Continuous transcript stream style */
.transcript-stream { font-size: 15px; line-height: 1.6; color: var(--teams-text); white-space: pre-wrap; word-break: break-word; }

.transcript-text { font-size: 15px; line-height: 1.6; color: var(--teams-text); }

/* Speaker change break */
.speaker-break { height: 12px; margin: 6px 0; border-bottom: 1px dashed rgba(255,255,255,0.08); }

/* Remove legacy card styles */
.transcript-entry, .transcript-header, .confidence-score { display: none !important; }

.transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: var(--teams-text-secondary);
}

.speaker-name {
    font-weight: 600;
    color: var(--teams-primary);
}

.timestamp {
    font-family: 'Courier New', monospace;
}

.transcript-text {
    font-size: 14px;
    line-height: 1.4;
}

.confidence-score {
    font-size: 11px;
    color: var(--teams-text-secondary);
    margin-top: 4px;
}

.no-transcript {
    text-align: center;
    color: var(--teams-text-secondary);
    font-style: italic;
    padding: 40px 20px;
}

.transcript-controls {
    margin-top: 12px;
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
}

/* Modal */
.modal { position: fixed; inset: 0; background: rgba(0,0,0,.6); display: flex; align-items: center; justify-content: center; z-index: 2000; }
.modal-content { background:#232326; border:1px solid #2e2e32; border-radius:10px; min-width:320px; max-width:520px; width:90%; box-shadow:0 20px 60px rgba(0,0,0,.6); }
.modal-header { display:flex; align-items:center; justify-content:space-between; padding:12px 16px; border-bottom:1px solid #2e2e32; }
.modal-body { padding:16px; }
.icon-btn { background:transparent; border:none; color:var(--teams-text); font-size:18px; cursor:pointer; }
.icon-btn:hover { opacity:.8; }

.footer {
    background-color: var(--teams-surface);
    border-top: 1px solid #E1DFDD;
    padding: 12px 20px;
    text-align: center;
    color: var(--teams-text-secondary);
    font-size: 12px;
}

/* Configure page styles */
.configure-page {
    background-color: var(--teams-surface);
    padding: 20px;
}

.configure-page .container {
    max-width: 600px;
    margin: 0 auto;
}

.configure-page .header {
    text-align: center;
    margin-bottom: 32px;
    border-bottom: none;
    padding-bottom: 0;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 16px;
}

.configure-page h1 {
    font-size: 24px;
    color: var(--teams-primary);
    margin-bottom: 8px;
}

.configure-page p {
    color: var(--teams-text-secondary);
}

.config-form {
    background-color: var(--teams-background);
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--teams-text);
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.actions {
    text-align: center;
}

.actions button {
    margin: 0 8px;
    min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 16px;
    }

    .control-buttons {
        flex-direction: column;
    }

    .recording-status {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .transcript-controls {
        flex-direction: column;
    }
}

/* Notification System */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease;
}

.notification:hover {
    opacity: 0.9 !important;
    transform: scale(1.02);
}

.notification.dismissing {
    animation: slideOut 0.3s ease-in;
}

/* Enhanced status indicators */
.status-dot.connecting {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Meeting selection styles */
.meeting-selection {
    background: var(--teams-surface);
    border: 2px solid var(--teams-accent);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.meeting-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.meeting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--teams-background);
    border-radius: 6px;
    border: 1px solid var(--teams-accent);
}

.meeting-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.meeting-info strong {
    color: var(--teams-text);
    font-weight: 600;
}

.meeting-info small {
    color: var(--teams-text-secondary);
    font-size: 12px;
}

.connect-btn {
    background: var(--teams-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.connect-btn:hover {
    background: var(--teams-secondary);
}