using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace AccureMD.TeamsBot.Models;

[Table("transcripts")]
public class TranscriptModel
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    [Column("meeting_id")]
    public string? MeetingId { get; set; }

    [Column("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    [Column("speaker_id")]
    public string? SpeakerId { get; set; }

    [Column("speaker_name")]
    public string? SpeakerName { get; set; } // store as NULL for anonymity

    [Column("text")]
    public string? Text { get; set; }

    [Column("confidence")]
    public double? Confidence { get; set; } // store as NULL when unknown/NA

    [Column("offset_from_start")]
    public TimeSpan OffsetFromStart { get; set; }

    // Navigation properties
    [ForeignKey("MeetingId")]
    public virtual MeetingModel? Meeting { get; set; }

    [NotMapped]
    [JsonPropertyName("speakerBoundary")]
    public bool SpeakerBoundary { get; set; } = false;
}

public class LiveTranscriptionUpdate
{
    [JsonPropertyName("meetingId")]
    public string MeetingId { get; set; } = string.Empty;

    [JsonPropertyName("transcript")]
    public TranscriptModel Transcript { get; set; } = new();

    [JsonPropertyName("isPartial")]
    public bool IsPartial { get; set; } = false;
}

public class TranscriptionSettings
{
    public string Language { get; set; } = "en-US";
    public bool EnableSpeakerIdentification { get; set; } = true;
    public bool EnablePunctuation { get; set; } = true;
    public bool EnableWordLevelTimestamps { get; set; } = true;
}