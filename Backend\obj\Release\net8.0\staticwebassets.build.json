{"Version": 1, "Hash": "aYPKbTYTmD01icEYg14hPujSTRZixx8Pk4X1wGLTo4M=", "Source": "AccureMD.TeamsBot", "BasePath": "_content/AccureMD.TeamsBot", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AccureMD.TeamsBot\\wwwroot", "Source": "AccureMD.TeamsBot", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "Pattern": "**"}], "Assets": [{"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8ywd3owvkn", "Integrity": "EOuBI37Eyu0IC2PE2NzHCvxvBivlBGAgepnTbxRx0gk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 17316, "LastWriteTime": "2025-08-30T21:49:25+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q6j5iap63k", "Integrity": "nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 28396, "LastWriteTime": "2025-08-10T18:39:37+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ksq8v2n24n", "Integrity": "XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 6131, "LastWriteTime": "2025-08-10T12:33:49+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zbbusfory5", "Integrity": "3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-test.html", "FileLength": 11310, "LastWriteTime": "2025-08-09T13:11:16+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9hvjguhige", "Integrity": "Hf6PpfKcb8kBVbR4OaYzo0IZ2LcVOuiu8KMQ027++k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 9734, "LastWriteTime": "2025-08-31T10:16:46+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/test-auth-fix#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ew7c11alxw", "Integrity": "AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\test-auth-fix.html", "FileLength": 9642, "LastWriteTime": "2025-08-10T12:36:26+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ukfhyo8vqk", "Integrity": "4pSwrtISWQKkXU2wqPpEuDsDVeNMOpKK4CD7C1y2C4M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 44206, "LastWriteTime": "2025-08-31T10:32:49+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-teams-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r54zswcdn2", "Integrity": "zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-teams-auth.html", "FileLength": 9705, "LastWriteTime": "2025-08-08T20:45:49+00:00"}], "Endpoints": [{"Route": "css/teams-app.8ywd3owvkn.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17316"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EOuBI37Eyu0IC2PE2NzHCvxvBivlBGAgepnTbxRx0gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 30 Aug 2025 21:49:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8ywd3owvkn"}, {"Name": "label", "Value": "css/teams-app.css"}, {"Name": "integrity", "Value": "sha256-EOuBI37Eyu0IC2PE2NzHCvxvBivlBGAgepnTbxRx0gk="}]}, {"Route": "css/teams-app.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17316"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EOuBI37Eyu0IC2PE2NzHCvxvBivlBGAgepnTbxRx0gk=\""}, {"Name": "Last-Modified", "Value": "Sat, 30 Aug 2025 21:49:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EOuBI37Eyu0IC2PE2NzHCvxvBivlBGAgepnTbxRx0gk="}]}, {"Route": "html/auth-callback.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28396"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 18:39:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk="}]}, {"Route": "html/auth-callback.q6j5iap63k.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28396"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 18:39:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q6j5iap63k"}, {"Name": "label", "Value": "html/auth-callback.html"}, {"Name": "integrity", "Value": "sha256-nkXzrlmnpnmrWFk15OJL5xOKQxCbZbNA6e5lP2C9bRk="}]}, {"Route": "html/auth-start.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}]}, {"Route": "html/auth-start.ksq8v2n24n.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ksq8v2n24n"}, {"Name": "label", "Value": "html/auth-start.html"}, {"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}]}, {"Route": "html/auth-test.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}]}, {"Route": "html/auth-test.zbbusfory5.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zbbusfory5"}, {"Name": "label", "Value": "html/auth-test.html"}, {"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}]}, {"Route": "html/configure.8inynrhuf2.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inynrhuf2"}, {"Name": "label", "Value": "html/configure.html"}, {"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/configure.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/index.9hvjguhige.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9734"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hf6PpfKcb8kBVbR4OaYzo0IZ2LcVOuiu8KMQ027++k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 31 Aug 2025 10:16:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9hvjguhige"}, {"Name": "label", "Value": "html/index.html"}, {"Name": "integrity", "Value": "sha256-Hf6PpfKcb8kBVbR4OaYzo0IZ2LcVOuiu8KMQ027++k4="}]}, {"Route": "html/index.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9734"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hf6PpfKcb8kBVbR4OaYzo0IZ2LcVOuiu8KMQ027++k4=\""}, {"Name": "Last-Modified", "Value": "Sun, 31 Aug 2025 10:16:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hf6PpfKcb8kBVbR4OaYzo0IZ2LcVOuiu8KMQ027++k4="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "label", "Value": "html/privacy.html"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/privacy.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/termsofuse.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "label", "Value": "html/termsofuse.html"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/test-auth-fix.ew7c11alxw.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ew7c11alxw"}, {"Name": "label", "Value": "html/test-auth-fix.html"}, {"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}]}, {"Route": "html/test-auth-fix.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}]}, {"Route": "js/teams-app.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44206"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4pSwrtISWQKkXU2wqPpEuDsDVeNMOpKK4CD7C1y2C4M=\""}, {"Name": "Last-Modified", "Value": "Sun, 31 Aug 2025 10:32:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4pSwrtISWQKkXU2wqPpEuDsDVeNMOpKK4CD7C1y2C4M="}]}, {"Route": "js/teams-app.ukfhyo8vqk.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44206"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4pSwrtISWQKkXU2wqPpEuDsDVeNMOpKK4CD7C1y2C4M=\""}, {"Name": "Last-Modified", "Value": "Sun, 31 Aug 2025 10:32:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ukfhyo8vqk"}, {"Name": "label", "Value": "js/teams-app.js"}, {"Name": "integrity", "Value": "sha256-4pSwrtISWQKkXU2wqPpEuDsDVeNMOpKK4CD7C1y2C4M="}]}, {"Route": "teams-test.cztlu2xxj7.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cztlu2xxj7"}, {"Name": "label", "Value": "teams-test.html"}, {"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "teams-test.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "label", "Value": "test-auth.html"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-auth.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-teams-auth.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}, {"Route": "test-teams-auth.r54zswcdn2.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r54zswcdn2"}, {"Name": "label", "Value": "test-teams-auth.html"}, {"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}]}