// AccureMD Teams App JavaScript

class TeamsApp {
    constructor() {
        console.log('AccureMD: TeamsApp constructor called');

        this.currentUser = null;
        this.currentMeeting = null;
        this.isRecording = false;
        this.isTranscribing = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.transcripts = [];
        this.transcriptIntervalId = null;
        this.teamsContext = null;
        this.isInTeams = false; // Will be set during initialization

        console.log('AccureMD: Starting initialization...');
        this.initializeTeamsContext();
        this.setupEventListeners();
        console.log('AccureMD: Constructor completed');
    }

    detectTeamsEnvironment() {
        // Check multiple indicators to determine if we're in Teams
        try {
            const inIframe = window.self !== window.top;
            const url = window.location.href;
            const hasTeamsUrl = url.includes('teams.microsoft.com') || url.includes('teams.live.com');
            const urlParams = new URLSearchParams(window.location.search);
            const hasTeamsParams = urlParams.has('inTeams') || urlParams.has('theme');
            const userAgent = navigator.userAgent.toLowerCase();
            const hasTeamsUserAgent = userAgent.includes('teams');

            let hasTeamsParent = false;
            try {
                hasTeamsParent = inIframe && window.parent && typeof window.parent.microsoftTeams !== 'undefined';
            } catch (e) {
                hasTeamsParent = inIframe; // Cross-origin access blocked, likely in Teams
            }

            const isInTeams = hasTeamsUrl || hasTeamsParams || hasTeamsUserAgent || hasTeamsParent;
            console.log('AccureMD: Teams environment detection result:', isInTeams);
            return isInTeams;
        } catch (error) {
            console.warn('AccureMD: Error detecting Teams environment:', error);
            return false;
        }
    }

    async handleAuthenticationReturn() {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');
        const authSuccess = urlParams.get('auth');

        if (error) {
            console.error('AccureMD: Authentication error in URL:', error);
            this.showError('Authentication failed: ' + error);
            window.history.replaceState({}, document.title, window.location.pathname);
            return;
        }

        // Handle auth=success parameter from auth-callback.html redirect
        if (authSuccess === 'success') {
            console.log('AccureMD: Authentication success parameter found, cleaning URL and checking status...');
            window.history.replaceState({}, document.title, window.location.pathname);
            await this.checkAuthenticationStatus();
            return;
        }

        if (code && state) {
            console.log('AccureMD: Found authentication code in URL, processing...');
            try {
                const response = await fetch('/api/auth/callback', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ code, state })
                });

                if (response.ok) {
                    console.log('AccureMD: Authentication callback successful');
                    window.history.replaceState({}, document.title, window.location.pathname);
                    await this.checkAuthenticationStatus();
                } else {
                    throw new Error('Authentication callback failed');
                }
            } catch (err) {
                console.error('AccureMD: Error processing authentication callback:', err);
                this.showError('Authentication processing failed');
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }
    }

    async initializeTeamsContext() {
        console.log('AccureMD: Starting app initialization...');
        await this.handleAuthenticationReturn();

        if (typeof microsoftTeams === 'undefined') {
            console.log('AccureMD: Teams SDK not available, running in standalone mode.');
            this.isInTeams = false;
            this.hideLoading();
            await this.checkAuthenticationStatus();
            return;
        }

        try {
            console.log('AccureMD: Initializing Teams SDK...');
            await microsoftTeams.app.initialize();
            console.log('AccureMD: Teams SDK initialized successfully.');
            this.isInTeams = true; // Set based on successful initialization
            this.teamsContext = await microsoftTeams.app.getContext();
            console.log('AccureMD: Teams context obtained:', this.teamsContext);
        } catch (initError) {
            console.warn('AccureMD: Teams SDK initialization failed, running in browser mode:', initError);
            this.isInTeams = false;
        }

        this.hideLoading();
        await this.checkAuthenticationStatus();
    }

    setupEventListeners() {
        document.getElementById('loginBtn')?.addEventListener('click', () => this.login());
        document.getElementById('joinBtn')?.addEventListener('click', () => this.startConnectionFlow());
        document.getElementById('endSessionBtn')?.addEventListener('click', () => this.endSession());

        // Transcription controls
        document.getElementById('startTranscriptionBtn')?.addEventListener('click', () => this.handleStartTranscription());
        document.getElementById('pauseTranscriptionBtn')?.addEventListener('click', () => this.handlePauseTranscription());
        document.getElementById('clearTranscriptBtn')?.addEventListener('click', () => this.clearTranscripts());
        document.getElementById('downloadTranscriptBtn')?.addEventListener('click', () => this.downloadTranscripts());

        // More menu (three dots)
        const moreBtn = document.getElementById('moreBtn');
        const moreMenu = document.getElementById('moreMenu');
        moreBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = moreMenu.style.display === 'block';
            moreMenu.style.display = isOpen ? 'none' : 'block';
            moreBtn.setAttribute('aria-expanded', (!isOpen).toString());
        });
        document.addEventListener('click', (e) => {
            if (!moreMenu) return;
            if (moreMenu.style.display === 'block' && !moreMenu.contains(e.target) && e.target !== moreBtn) {
                moreMenu.style.display = 'none';
                moreBtn?.setAttribute('aria-expanded', 'false');
            }
        });
        document.getElementById('menuProfile')?.addEventListener('click', () => this.openProfileModal());
        document.getElementById('menuSignOut')?.addEventListener('click', () => this.logout());
        document.getElementById('closeProfileModal')?.addEventListener('click', () => this.closeProfileModal());
    }

    async checkAuthenticationStatus() {
        try {
            let userId = 'anonymous';
            if (this.isInTeams && this.teamsContext?.user?.id) {
                userId = this.teamsContext.user.id;
                console.log('AccureMD: Got Teams user ID:', userId);
            }

            console.log('AccureMD: Checking auth status for user:', userId);
            const response = await fetch(`/api/auth/status/${userId}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const authStatus = await response.json();
            console.log('AccureMD: Auth status response:', authStatus);

            if (authStatus.isAuthenticated) {
                console.log('AccureMD: User is authenticated, showing main app.');
                this.currentUser = authStatus;
                this.showMainApp();
                this.updateUserInterface();
                // After successful auth, try to rehydrate any active meeting session for this user
                try {
                    const activeResp = await fetch(`/api/meetings/active/${userId}`);
                    if (activeResp.ok) {
                        const activeMeetings = await activeResp.json();
                        if (Array.isArray(activeMeetings) && activeMeetings.length > 0) {
                            const m = activeMeetings[0];
                            this.currentMeeting = { id: m.id, url: m.meetingUrl };
                            // Reflect state
                            this.updateConnectionStatus('Connected to meeting', 'online');
                            // Persist connection flow UI while session is active
                            const form = document.getElementById('joinForm');
                            const flow = document.getElementById('connectionFlow');
                            if (form && flow) { form.style.display = 'none'; flow.style.display = 'block'; }
                            ['flowDot1','flowDot2','flowDot3'].forEach(id=>{ const el=document.getElementById(id); if(el) el.className='status-dot online'; });
                            // Hide spinner if present
                            const prog = document.querySelector('#connectionFlow .flow-progress');
                            if (prog) prog.style.display = 'none';
                            this.showRecordingControls();
                            // Reflect recording/transcribing flags if present
                            this.isRecording = !!m.isRecording;
                            this.isTranscribing = !!m.isTranscribing;
                            this.updateRecordingInterface();
                            this.toggleTranscriptionButtons({ start: !this.isTranscribing, pause: this.isTranscribing });
                            // Show connection details (meeting title, participants)
                            await this.updateConnectionDetails(m);
                            // Resume polling
                            this.startTranscriptPolling();
                        }
                    }
                } catch (rehydrateErr) {
                    console.warn('AccureMD: Session rehydrate failed', rehydrateErr);
                }

            } else {
                console.log('AccureMD: User is not authenticated, showing auth screen.');
                this.showAuthScreen();
            }
        } catch (error) {
            console.error('AccureMD: Error checking authentication:', error);
            this.showAuthScreen(); // Default to auth screen on error
        }
    }

    /**
     * Helper method to detect Teams desktop app
     */
    isTeamsDesktopApp() {
        const userAgent = navigator.userAgent;
        const isTeamsDesktop = userAgent.includes('Teams/');
        console.log('AccureMD: Teams desktop app detection - UserAgent:', userAgent);
        console.log('AccureMD: Is Teams desktop app:', isTeamsDesktop);
        return isTeamsDesktop;
    }

    /**
     * [REVISED] Initiates the login flow using the Teams SDK.
     */
    async login() {
        console.log('AccureMD: Starting login process with Teams SDK...');
        console.log('AccureMD: Teams desktop app detected:', this.isTeamsDesktopApp());

        // The primary method should be the Teams SDK.
        if (!this.isInTeams) {
            this.showError("This app is designed for Microsoft Teams. Authentication might not work correctly in a browser.");
            console.warn("Not in Teams environment. The legacy popup method is not recommended and likely to be blocked.");
            // You could optionally call a fallback legacy method here, but it's unreliable.
            return;
        }

        try {
            const userId = this.teamsContext?.user?.id || 'anonymous';
            const redirectUri = `${window.location.origin}/html/auth-callback.html`;

            console.log('AccureMD: Starting Teams authentication flow...');
            console.log('AccureMD: Using redirect URI:', redirectUri);
            console.log('AccureMD: User ID:', userId);

            // Build authentication start URL hosted on our domain (Teams requirement)
            const authStartUrl = `${window.location.origin}/html/auth-start.html?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;
            console.log('AccureMD: Using auth start URL:', authStartUrl);

            // Add message listener for fallback communication
            const messageHandler = (event) => {
                if (event.data && event.data.type === 'teams-auth-complete' && event.data.success) {
                    console.log('AccureMD: Received fallback auth success message:', event.data);
                    window.removeEventListener('message', messageHandler);

                    // Process the auth data from the fallback message
                    if (event.data.data && event.data.data.accessToken) {
                        console.log('AccureMD: Processing fallback authentication data');
                        this.showNotification('Authentication successful! Updating session...', 'success');

                        // Check authentication status after a delay
                        setTimeout(async () => {
                            await this.checkAuthenticationStatus();
                        }, 1000);
                    }
                }
            };
            window.addEventListener('message', messageHandler);

            microsoftTeams.authentication.authenticate({
                url: authStartUrl,
                width: 600,
                height: 700,
                hostRedirectUrl: redirectUri,
                successCallback: async (resultKey) => {
                    console.log('AccureMD: Teams auth flow succeeded. Result key:', resultKey);
                    window.removeEventListener('message', messageHandler); // Clean up fallback listener

                    try {
                        // Retrieve the authentication data from localStorage using the key
                        const authDataJson = localStorage.getItem(resultKey);
                        if (authDataJson) {
                            const authData = JSON.parse(authDataJson);
                            console.log('AccureMD: Retrieved auth data:', authData);

                            // Clean up the localStorage entry
                            localStorage.removeItem(resultKey);

                            // Store the authentication data for the app to use
                            if (authData.accessToken) {
                                console.log('AccureMD: Authentication data received successfully');
                                this.showNotification('Authentication successful! Updating session...', 'success');
                            }
                        } else {
                            console.warn('AccureMD: No auth data found for key:', resultKey);
                        }
                    } catch (error) {
                        console.error('AccureMD: Error processing auth result:', error);
                    }

                    // Add a small delay to ensure backend has processed the authentication
                    console.log('AccureMD: Waiting for backend to process authentication...');
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // After processing the auth data, check the authentication status
                    await this.checkAuthenticationStatus();
                },
                failureCallback: (reason) => {
                    // This is called if the user closes the popup or an error occurs.
                    console.error('AccureMD: Teams auth flow failed:', reason);
                    console.error('AccureMD: Failure details - Auth Start URL:', authStartUrl);
                    console.error('AccureMD: Failure details - Redirect URI:', redirectUri);
                    console.error('AccureMD: Failure details - User ID:', userId);

                    // Clean up fallback listener
                    window.removeEventListener('message', messageHandler);

                    // Special handling for CancelledByUser error in Teams desktop app
                    if (reason === 'CancelledByUser') {
                        console.log('AccureMD: CancelledByUser error detected - this might be the Teams desktop app bug');
                        console.log('AccureMD: Waiting a moment to see if authentication actually succeeded...');

                        // Wait a bit and then check if authentication actually succeeded
                        setTimeout(async () => {
                            console.log('AccureMD: Checking authentication status after CancelledByUser error...');
                            try {
                                await this.checkAuthenticationStatus();

                                // If we're still not authenticated after checking, show the error
                                if (!this.isAuthenticated) {
                                    this.showError(`Authentication failed or was cancelled: ${reason}`);
                                }
                            } catch (error) {
                                console.error('AccureMD: Error checking auth status after CancelledByUser:', error);
                                this.showError(`Authentication failed or was cancelled: ${reason}`);
                            }
                        }, 2000);
                    } else {
                        this.showError(`Authentication failed or was cancelled: ${reason}`);
                    }
                },
            });

        } catch (error) {
            console.error('AccureMD: Login process failed:', error);
            this.showError(`An error occurred during login: ${error.message}`);
        }
    }

    /**
     * [REMOVED] The openAuthPopup and its fallback logic are no longer the primary method
     * and were the source of the errors. This logic is replaced by the `microsoftTeams.authentication.authenticate` call.
     */
    // openAuthPopup(authUrl) { ... } // This function is no longer needed.

    openProfileModal() {
        const modal = document.getElementById('profileModal');
        if (modal) modal.style.display = 'flex';
    }
    closeProfileModal() {
        const modal = document.getElementById('profileModal');
        if (modal) modal.style.display = 'none';
    }

    async logout() {
        try {
            const userId = this.currentUser?.userId || 'anonymous';
            await fetch(`/api/auth/logout/${userId}`, { method: 'POST' });
            this.currentUser = null;
            this.currentMeeting = null;
            this.showAuthScreen();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    // --- All other methods (joinMeeting, leaveMeeting, UI helpers, etc.) remain the same ---
    // ... (rest of your file)

    async startConnectionFlow() {
        try {
            const meetingUrl = document.getElementById('meetingUrl').value.trim();
            if (!this.validateMeetingUrl(meetingUrl)) return;

            // Switch UI to staged connection flow and keep it for the whole session
            const form = document.getElementById('joinForm');
            const flow = document.getElementById('connectionFlow');
            if (form && flow) { form.style.display = 'none'; flow.style.display = 'block'; }

            // Stage 1
            this.updateConnectionStatus('Connecting to the meeting', 'connecting');
            const setDot = (id, state) => {
                const el = document.getElementById(id);
                if (el) el.className = `status-dot ${state}`;
            };
            setDot('flowDot1', 'connecting');
            setDot('flowDot2', 'offline');
            setDot('flowDot3', 'offline');
            await this.sleep(2000);

            // Stage 2
            setDot('flowDot2', 'connecting');
            this.updateConnectionStatus('Media Bot is joining the meeting', 'connecting');
            await this.sleep(2000);

            // Stage 3: actual join call
            const response = await fetch('/api/meetings/join', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    meetingUrl,
                    userId: this.currentUser?.userId || 'anonymous',
                    displayName: 'AccureMD Bot'
                })
            });
            if (!response.ok) throw new Error('Failed to join meeting');
            const result = await response.json();
            if (!result.success) throw new Error(result.message || 'Join failed');

            this.currentMeeting = { id: result.meetingId, url: meetingUrl };
            setDot('flowDot3', 'online');
            this.updateConnectionStatus('Connection established', 'online');
            // Stop loader and reflect success details
            const prog = document.querySelector('#connectionFlow .flow-progress');
            if (prog) prog.style.display = 'none';
            const step3 = document.getElementById('flowText3');
            if (step3) step3.textContent = 'Connection established';
            await this.updateConnectionDetails();

            // Show controls and End Session button
            this.showRecordingControls();
            const endBtn = document.getElementById('endSessionBtn');
            if (endBtn) endBtn.style.display = 'inline-flex';

            // Auto-start recording+transcription
            try {
                await this.startRecording();
                this.isTranscribing = true;
                this.updateRecordingInterface();
                this.toggleTranscriptionButtons({ start: false, pause: true });
            } catch {}
        } catch (error) {
            this.updateConnectionStatus('Connection failed', 'offline');
            this.showError(`Error joining meeting: ${error.message}`);
            // Revert UI on failure
            const form = document.getElementById('joinForm');
            const flow = document.getElementById('connectionFlow');
            if (form && flow) { form.style.display = 'block'; flow.style.display = 'none'; }
        }
    }

    sleep(ms) { return new Promise(res => setTimeout(res, ms)); }

    async leaveMeeting() {
        if (!this.currentMeeting) return;
        if (this.isRecording) await this.stopRecording();
        await fetch(`/api/meetings/${this.currentMeeting.id}/leave`, { method: 'POST' });
        this.currentMeeting = null;
        this.updateConnectionStatus('Ready to join', 'offline');
        this.hideRecordingControls();
    }


	    async endSession() {
	        try {
	            if (this.isRecording) {
	                await this.stopRecording();
	            }
	            if (this.currentMeeting) {
	                await fetch(`/api/meetings/${this.currentMeeting.id}/leave`, { method: 'POST' });
	            }
	            this.currentMeeting = null;
	            this.updateConnectionStatus('Session ended', 'offline');
	            this.hideRecordingControls();
            // Restore Join Meeting form and hide connection flow
            const form = document.getElementById('joinForm');
            const flow = document.getElementById('connectionFlow');
            if (form && flow) { form.style.display = 'block'; flow.style.display = 'none'; }
            const endBtn = document.getElementById('endSessionBtn');
            if (endBtn) endBtn.style.display = 'none';
	            this.stopTranscriptPolling();
	            this.clearTranscripts();
	            this.showNotification('Session ended', 'success');
	        } catch (e) {
	            this.showError(`Could not end session: ${e.message}`);
	        }
	    }

    async startRecording() {
        if (!this.currentMeeting) return;
        const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, { method: 'POST' });
        const result = await response.json();
        if (result.success) {
            this.isRecording = true;
            // Backend StartRecording also attempts to start transcription
            this.isTranscribing = true;
            this.updateRecordingInterface();
            this.startTranscriptPolling();
            this.toggleTranscriptionButtons({ start: false, pause: true });
        } else {
            this.showError(result.message);
        }
    }

    async stopRecording() {
        if (!this.currentMeeting) return;
        const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/stop`, { method: 'POST' });
        const result = await response.json();
        if (result.success) {
            this.isRecording = false;
            this.isTranscribing = false;
            this.updateRecordingInterface();
            this.stopTranscriptPolling();
            this.toggleTranscriptionButtons({ start: true, pause: false });
        } else {
            this.showError(result.message);
        }
    }

        // --- Transcription Control Handlers ---
        async handleStartTranscription() {
            try {
                if (!this.currentMeeting) return;
                if (!this.isRecording) {
                    // Starting transcription implicitly starts recording via backend StartRecording
                    await this.startRecording();
                } else if (!this.isTranscribing) {
                    // If recording is active but transcription is not, call a dedicated endpoint if available
                    // Fallback: toggle UI state only
                    try {
                        const resp = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, { method: 'POST' });
                        if (!resp.ok) throw new Error('Failed to ensure recording');
                    } catch {}
                    this.isTranscribing = true;
                    this.updateRecordingInterface();
                }
                // Start polling transcripts and toggle controls
                this.startTranscriptPolling();
                this.toggleTranscriptionButtons({ start: false, pause: true });
                this.showNotification('Transcription started', 'success');
            } catch (e) {
                this.showError(`Could not start transcription: ${e.message}`);
            }
        }

        async handlePauseTranscription() {
            try {
                if (!this.currentMeeting) return;
                const mid = this.currentMeeting.id;
                const btn = document.getElementById('pauseTranscriptionBtn');
                if (this.isTranscribing) {
                    // Pause
                    const resp = await fetch(`/api/meetings/${mid}/transcription/pause`, { method: 'POST' });
                    if (!resp.ok) throw new Error('Pause failed');
                    this.isTranscribing = false;
                    btn.textContent = 'Resume';
                    this.stopTranscriptPolling();
                    this.showNotification('Transcription paused', 'info');
                } else {
                    // Resume
                    const resp = await fetch(`/api/meetings/${mid}/transcription/resume`, { method: 'POST' });
                    if (!resp.ok) throw new Error('Resume failed');
                    this.isTranscribing = true;
                    btn.textContent = 'Pause';
                    this.startTranscriptPolling();
                    this.showNotification('Transcription resumed', 'info');
                }
                this.updateRecordingInterface();
            } catch (e) {
                this.showError(`Could not toggle transcription: ${e.message}`);
            }
        }

        async handleStopTranscription() {
            try {
                if (!this.currentMeeting) return;
                // Stop recording will also stop transcription on backend
                await this.stopRecording();
                // Reset UI controls
                this.toggleTranscriptionButtons({ start: true, pause: false });
                const pauseBtn = document.getElementById('pauseTranscriptionBtn');
                pauseBtn.textContent = 'Pause';
                this.showNotification('Transcription stopped', 'success');
            } catch (e) {
                this.showError(`Could not stop transcription: ${e.message}`);
            }
        }

        toggleTranscriptionButtons(state) {
            // state: { start: boolean, pause: boolean }
            const startBtn = document.getElementById('startTranscriptionBtn');
            const pauseBtn = document.getElementById('pauseTranscriptionBtn');
            if (startBtn) startBtn.disabled = !state.start;
            if (pauseBtn) pauseBtn.disabled = !state.pause;
        }

        startTranscriptPolling() {
            if (!this.currentMeeting) return;
            if (this.transcriptIntervalId) return;
            this.renderedTranscriptCount = 0; // for incremental rendering
            this.transcriptIntervalId = setInterval(async () => {
                try {
                    const resp = await fetch(`/api/meetings/${this.currentMeeting.id}/transcripts`);
                    if (!resp.ok) return;
                    const data = await resp.json();
                    this.renderLiveTranscripts(data || []);
                } catch {}
            }, 1000); // poll every 1s for lower latency
        }

        stopTranscriptPolling() {
            if (this.transcriptIntervalId) {
                clearInterval(this.transcriptIntervalId);
                this.transcriptIntervalId = null;
            }
        }

        renderLiveTranscripts(items) {
            const container = document.getElementById('transcriptContainer');
            if (!container) return;

            // When no items, reset placeholder and state
            if (!items.length) {
                container.innerHTML = '<div class="no-transcript">Live captions will appear here in real time...</div>';
                this.transcripts = [];
                this.renderedTranscriptCount = 0;
                this._lastRenderedSpeaker = null;
                this._streamText = '';
                return;
            }

            // Initial setup: single continuous stream element
            let streamEl = container.querySelector('#transcriptStream');
            if (!this.renderedTranscriptCount) {
                container.innerHTML = '';
                streamEl = document.createElement('div');
                streamEl.id = 'transcriptStream';
                streamEl.className = 'transcript-stream';
                streamEl.textContent = '';
                container.appendChild(streamEl);
                this._autoscroll = true;
                this._lastRenderedSpeaker = null;
                this._streamText = '';
            }

            // Append only new items into the continuous stream
            for (let i = this.renderedTranscriptCount || 0; i < items.length; i++) {
                const t = items[i] || {};
                const isBoundary = !!t.speakerBoundary || (!t.text && t.text !== undefined);

                if (isBoundary) {
                    // Speaker change: append a single space (no name/label)
                    streamEl.textContent += ' ';
                    this._lastRenderedSpeaker = null;
                    continue;
                }

                const chunk = (t.text || '').trim();
                if (!chunk) continue;

                // Ensure separation by a single space
                const needsSpace = streamEl.textContent && !streamEl.textContent.endsWith(' ');
                streamEl.textContent += (needsSpace ? ' ' : '') + chunk;
                this._lastRenderedSpeaker = ''; // intentionally ignore name/labels
            }

            if (this._autoscroll) {
                container.scrollTop = container.scrollHeight;
            }

            this.transcripts = items;
            this.renderedTranscriptCount = items.length;
        }

        clearTranscripts() {
            this.transcripts = [];
            const container = document.getElementById('transcriptContainer');
            if (container) {
                container.innerHTML = '<div class="no-transcript">Live captions will appear here in real time...</div>';
            }
        }

        async downloadTranscripts() {
            try {
                const mid = this.currentMeeting?.id;
                if (!mid) { this.showError('No active meeting'); return; }
                const resp = await fetch(`/api/transcription/${mid}/text`);
                if (!resp.ok) throw new Error('Download failed');
                const text = await resp.text();
                const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `AccureMD_Transcript_${mid}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (e) {
                this.showError(`Download failed: ${e.message}`);
            }
        }

    // --- Transcription Control Handlers ---
    async handleStartTranscription() {
        try {
            if (!this.currentMeeting) return;
            if (!this.isRecording) {
                // Starting transcription implicitly starts recording via backend StartRecording
                await this.startRecording();
            } else if (!this.isTranscribing) {
                // If recording is active but transcription is not, call a dedicated endpoint if available
                // Fallback: toggle UI state only
                try {
                    const resp = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, { method: 'POST' });
                    if (!resp.ok) throw new Error('Failed to ensure recording');
                } catch {}
                this.isTranscribing = true;
                this.updateRecordingInterface();
            }
            // Enable pause/stop, disable start
            this.toggleTranscriptionButtons({ start: false, pause: true });
            this.showNotification('Transcription started', 'success');
        } catch (e) {
            this.showError(`Could not start transcription: ${e.message}`);
        }
    }

    async handlePauseTranscription() {
        try {
            // Backend does not currently expose pause; we simulate pause on UI
            this.isTranscribing = !this.isTranscribing;
            const btn = document.getElementById('pauseTranscriptionBtn');
            btn.textContent = this.isTranscribing ? 'Pause' : 'Resume';
            this.updateRecordingInterface();
            this.showNotification(this.isTranscribing ? 'Transcription resumed' : 'Transcription paused', 'info');
        } catch (e) {
            this.showError(`Could not toggle transcription: ${e.message}`);
        }
    }

    // Removed StopTranscription: end session handles full stop

    toggleTranscriptionButtons(state) {
        // state: { start: boolean, pause: boolean }
        const startBtn = document.getElementById('startTranscriptionBtn');
        const pauseBtn = document.getElementById('pauseTranscriptionBtn');
        if (startBtn) startBtn.disabled = !state.start;
        if (pauseBtn) pauseBtn.disabled = !state.pause;
    }

    // --- UI Helper Methods ---
    hideLoading() { document.getElementById('loadingScreen').style.display = 'none'; }
    showAuthScreen() {
        document.getElementById('authScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
    }
    showMainApp() {
        document.getElementById('authScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'flex';
    }
    updateUserInterface() {
        if (this.currentUser) {
            const userName = this.currentUser.userName || 'User';
            const email = this.currentUser.email || '';
            const userId = this.currentUser.userId || '';
            const tokenExpiry = this.currentUser.tokenExpiry ? new Date(this.currentUser.tokenExpiry).toLocaleString() : '-';
            const permissions = Array.isArray(this.currentUser.permissions) ? this.currentUser.permissions.join(', ') : '-';

            const initial = userName.charAt(0).toUpperCase();
            document.getElementById('userName').textContent = userName;
            document.getElementById('userAvatar').textContent = initial;
            const profileAvatarEl = document.getElementById('profileAvatar');
            if (profileAvatarEl) profileAvatarEl.textContent = initial;
            document.getElementById('profileMenuAvatar').textContent = initial;
            document.getElementById('profileMenuName').textContent = userName;
            document.getElementById('profileMenuEmail').textContent = email;
            document.getElementById('profileMenuUserId').textContent = userId;
            document.getElementById('profileMenuTokenExpiry').textContent = tokenExpiry;
            document.getElementById('profileMenuPermissions').textContent = permissions;
        }
    }
    updateConnectionStatus(text, status) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.querySelector('.status-dot').className = `status-dot ${status}`;
        statusElement.querySelector('span:last-child').textContent = text;

        // Auto start/stop session timer based on connection
        if (status === 'online') {
            this.startSessionTimer();
        } else if (status === 'offline') {
            this.stopSessionTimer();
        }
    }

    showRecordingControls() {
        const rc = document.getElementById('recordingControls');
        if (rc) rc.style.display = 'block';
        const endBtn = document.getElementById('endSessionBtn');
        if (endBtn) endBtn.style.display = 'inline-flex';
    }
    hideRecordingControls() {
        const rc = document.getElementById('recordingControls');
        if (rc) rc.style.display = 'none';
        const endBtn = document.getElementById('endSessionBtn');
        if (endBtn) endBtn.style.display = 'none';
    }
    updateRecordingInterface() {
        const recDot = document.getElementById('recordingDot');
        const recText = document.getElementById('recordingStatusText');
        const transDot = document.getElementById('transcriptionDot');
        const transText = document.getElementById('transcriptionStatusText');

        if (this.isRecording) {
            recDot.className = 'status-dot recording';
            recText.textContent = 'Recording: Active';
        } else {
            recDot.className = 'status-dot offline';
            recText.textContent = 'Recording: Idle';
        }

        if (this.isTranscribing) {
            transDot.className = 'status-dot online';
            transText.textContent = 'Transcription: Active';
        } else {
            transDot.className = 'status-dot offline';
            transText.textContent = 'Transcription: Idle';
        }
        // Sync control button states
        this.toggleTranscriptionButtons({ start: !this.isTranscribing, pause: this.isTranscribing });
    }

    startSessionTimer() {
        try {
            if (this.recordingTimer) return; // already running
            this.recordingStartTime = Date.now();
            const timerEl = document.getElementById('recordingTimer');
            this.recordingTimer = setInterval(() => {
                const elapsed = Date.now() - this.recordingStartTime;
                timerEl.textContent = this.formatDuration(elapsed);
            }, 1000);
        } catch (e) { console.warn('Timer start failed', e); }
    }

    stopSessionTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
        const timerEl = document.getElementById('recordingTimer');
        if (timerEl) timerEl.textContent = '00:00:00';
    }

    formatDuration(ms) {
        const totalSeconds = Math.floor(ms / 1000);
        const h = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
        const m = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
        const s = String(totalSeconds % 60).padStart(2, '0');
        return `${h}:${m}:${s}`;
    }
    validateMeetingUrl(url) {
        if (!url || !url.startsWith('https://teams.microsoft.com/')) {
            this.showError('Please enter a valid Microsoft Teams meeting URL.');
            return false;
        }
        return true;
    }
    showError(message) { this.showNotification(message, 'error'); }
    // Modern toast notification system (top-right stack)
    showNotification(message, type = 'info', duration = 4000) {
        // Ensure a single container exists
        let container = document.getElementById('toastContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toastContainer';
            container.setAttribute('aria-live', 'polite');
            container.style.cssText = `
                position: fixed; top: 16px; right: 16px; z-index: 3000;
                display: flex; flex-direction: column; gap: 8px; align-items: flex-end;`;
            document.body.appendChild(container);
        }
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.role = 'status';
        toast.style.cssText = `
            background: var(--teams-surface); color: var(--teams-text); border: 1px solid var(--teams-accent);
            box-shadow: 0 8px 24px rgba(0,0,0,.4); border-radius: 8px; padding: 10px 12px; display: flex; align-items: center; gap: 10px; min-width: 260px; max-width: 380px; opacity: 0; transform: translateY(-6px); transition: all .2s ease;`;
        const dot = document.createElement('span');
        dot.className = 'status-dot ' + (type === 'error' ? 'offline' : type === 'success' ? 'online' : 'recording');
        const text = document.createElement('div');
        text.textContent = message;
        text.style.flex = '1';
        const close = document.createElement('button');
        close.className = 'icon-btn';
        close.textContent = '✕';
        close.setAttribute('aria-label', 'Dismiss');
        close.onclick = () => toast.remove();
        toast.appendChild(dot); toast.appendChild(text); toast.appendChild(close);
        container.appendChild(toast);
        requestAnimationFrame(()=>{ toast.style.opacity='1'; toast.style.transform='translateY(0)';});
        setTimeout(()=>{ if (toast.parentNode) toast.remove(); }, duration);
    }

    // Display connection details and stop any loading spinner
    async updateConnectionDetails(meetingMeta) {
        try {
            const detailsEl = document.getElementById('flowDetails');
            if (!detailsEl) return;
            let data = meetingMeta;
            if (!data) {
                const userId = this.currentUser?.userId || 'anonymous';
                const resp = await fetch(`/api/meetings/active/${userId}`);
                if (resp.ok) {
                    const arr = await resp.json();
                    data = Array.isArray(arr) && arr.length ? arr[0] : null;
                }
            }
            const title = data?.title || 'Meeting';
            const participants = Array.isArray(data?.participants) ? data.participants.length : 1;
            detailsEl.style.display = 'block';
            detailsEl.textContent = `${title} • Connected • Participants: ${participants}`;
        } catch {}
    }
}
