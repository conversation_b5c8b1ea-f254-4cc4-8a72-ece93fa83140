{"Logging": {"LogLevel": {"Default": "Information"}}, "AllowedHosts": "*", "BotConfiguration": {"BotName": "AccureMD Media Bot", "AadAppId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "AadAppSecret": "****************************************", "ServiceCname": "accuremd.eastus.cloudapp.azure.com", "MediaServiceFQDN": "accuremd.eastus.cloudapp.azure.com", "ServiceDnsName": "accuremd.eastus.cloudapp.azure.com", "CertificateThumbprint": "813a886f7429f942edafcf931a0588d0cabd5ce8", "InstancePublicPort": 8445, "CallSignalingPort": 443, "InstanceInternalPort": 8445, "PlaceCallEndpointUrl": "https://graph.microsoft.com/v1.0", "PsiStoreDirectory": "D:/AccureMD/recordings", "AsrEndpoint": "http://************:40015/v1/audio/transcriptions", "AsrToken": "be7e16e764c4226b29e0acd909679a57", "AsrModel": "mistralai/voxtral-mini-3B-2507", "BackendRealtimeWebhook": "https://accuremd.azurewebsites.net/api/transcription/realtime"}}