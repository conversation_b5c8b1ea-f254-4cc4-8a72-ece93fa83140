using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Graph.Communications.Calls;
using Microsoft.Graph.Communications.Calls.Media;
using Microsoft.Graph.Communications.Common.Telemetry;
using Microsoft.Graph.Communications.Resources;
using Microsoft.Skype.Bots.Media;
using AccureMD.MediaBot.Worker.Settings;

namespace AccureMD.MediaBot.Worker.Services
{
    public class CallHandler : IDisposable
    {
        public ICall Call { get; }
        private readonly IGraphLogger _logger;
        private readonly BotConfiguration _config;
        private readonly IAudioSocket _audioSocket;
        private FileStream? _currentWav;
        private BinaryWriter? _writer;
        private string _currentFilePath = string.Empty;
        private readonly object _lock = new object();
        private long _dataLength = 0;
        // ASR streaming
        private readonly Services.Asr.AudioChunker _chunker = new Services.Asr.AudioChunker();
        private readonly Services.Asr.AsrClient _asrClient;
        private readonly HttpClient _webhookClient = new HttpClient();
        // Simple diarization state: increment speaker index on detected boundaries
        private int _speakerIndex = 1;
        private string _currentSpeakerLabel = "Speaker 1";
        private volatile bool _asrPaused = false;

        public CallHandler(ICall call, BotConfiguration config)
        {
            Call = call;
            _config = config;
            _logger = call.GraphLogger;

            _asrClient = new Services.Asr.AsrClient(
                endpoint: _config.AsrEndpoint,
                token: _config.AsrToken,
                model: _config.AsrModel,
                logger: _logger);

            var session = call.GetLocalMediaSession();
            _audioSocket = session.AudioSocket;
            _audioSocket.AudioMediaReceived += OnAudioMediaReceived;
            call.GraphLogger.Info($"CallHandler created for CallId={call.Id}; subscribing to OnUpdated and audio events.");
            Call.OnUpdated += (sender, e) =>
            {
                var state = e.NewResource.State?.ToString() ?? string.Empty;
                call.GraphLogger.Info($"Call {call.Id} state changed to '{state}'");
                if (state.Equals("Established", StringComparison.OrdinalIgnoreCase))
                {
                    StartNewWavFile();
                }
                else if (state.Equals("Terminated", StringComparison.OrdinalIgnoreCase))
                {
                    CloseWavFile();
                }
            };
        }

        private void StartNewWavFile()
        {
            try
            {
                var baseDir = string.IsNullOrWhiteSpace(_config.PsiStoreDirectory) ? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "recordings") : _config.PsiStoreDirectory;
                Directory.CreateDirectory(baseDir);
                _currentFilePath = Path.Combine(baseDir, $"mixed_{Call.Id}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.wav");
                _currentWav = new FileStream(_currentFilePath, FileMode.Create, FileAccess.Write, FileShare.Read);
                _writer = new BinaryWriter(_currentWav);
                WriteWavHeader(_writer, sampleRate: 16000, channels: 1, bitsPerSample: 16);
                _dataLength = 0;
                _logger.Info($"Started WAV recording: {_currentFilePath}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to create WAV file");
            }
        }

        private void CloseWavFile()
        {
            try
            {
                if (_writer != null && _currentWav != null)
                {
                    FinalizeWavHeader(_writer, _dataLength);
                    _writer.Flush();
                    _writer.Close();
                    _currentWav.Close();
                    _logger.Info($"Closed WAV recording: {_currentFilePath}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to close WAV file");
            }
            finally
            {
                _writer = null;
                _currentWav = null;
                _dataLength = 0;
                _currentFilePath = string.Empty;
            }
        }

        private void OnAudioMediaReceived(object sender, AudioMediaReceivedEventArgs e)
        {
            try
            {
                var buffer = e.Buffer;
                if (buffer.Length > 0 && buffer.Data != IntPtr.Zero)
                {
                    var bytes = new byte[buffer.Length];
                    System.Runtime.InteropServices.Marshal.Copy(buffer.Data, bytes, 0, (int)buffer.Length);

                    // 1) Persist to rolling WAV file
                    if (_writer != null)
                    {
                        lock (_lock)
                        {
                            _writer.Write(bytes);
                            _dataLength += bytes.Length;
                        }
                    }

                    // 2) Feed ASR chunker and attempt transcription asynchronously
                    _chunker.Append(bytes);
                    var chunk = _chunker.TryGetNextChunk();
                    if (chunk != null)
                    {
                        _ = Task.Run(async () =>
                        {
                            if (_asrPaused)
                            {
                                if (chunk.SpeakerBoundary)
                                {
                                    await PostRealtimeAsync(string.Empty, 0.0, true).ConfigureAwait(false);
                                }
                                return;
                            }
                            // Only call ASR when VAD indicates speech present
                            if (chunk.HasSpeech)
                            {
                                var text = await _asrClient.TranscribeAsync(chunk.Wav, System.Threading.CancellationToken.None).ConfigureAwait(false);
                                if (!string.IsNullOrWhiteSpace(text))
                                {
                                    await PostRealtimeAsync(text, 0.0, chunk.SpeakerBoundary).ConfigureAwait(false);
                                }
                            }
                            else if (chunk.SpeakerBoundary)
                            {
                                // Emit boundary without text to render a speaker break in UI
                                await PostRealtimeAsync(string.Empty, 0.0, true).ConfigureAwait(false);
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing audio");
            }
            finally
            {
                e.Buffer.Dispose();
            }
        }

        public void Dispose()
        {
            _audioSocket.AudioMediaReceived -= OnAudioMediaReceived;
            // Can't unsubscribe lambda; it's fine for disposal lifecycle
            CloseWavFile();
        }
        public void SetAsrPaused(bool paused)
        {
            _asrPaused = paused;
            _logger.Info($"SetAsrPaused={paused}");
        }

        private async Task PostRealtimeAsync(string text, double confidence, bool speakerBoundary)
        {
            try
            {
                var url = string.IsNullOrWhiteSpace(_config.BackendRealtimeWebhook)
                    ? "https://accuremd.eastus.cloudapp.azure.com/api/transcription/realtime"
                    : _config.BackendRealtimeWebhook;

                if (speakerBoundary)
                {
                    _speakerIndex = Math.Min(_speakerIndex + 1, 10);
                    _currentSpeakerLabel = $"Speaker {_speakerIndex}";
                }

                var payload = new
                {
                    CallId = Call.Id,
                    Text = text,
                    Confidence = confidence,
                    Timestamp = DateTimeOffset.UtcNow,
                    SpeakerName = _currentSpeakerLabel,
                    IsPartial = false,
                    SpeakerBoundary = speakerBoundary
                };

                var json = System.Text.Json.JsonSerializer.Serialize(payload);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                var resp = await _webhookClient.PostAsync(url, content).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                    _logger.Warn($"Realtime webhook POST failed {(int)resp.StatusCode}: {body}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error POSTing realtime transcript to backend");
            }
        }


        private static void WriteWavHeader(BinaryWriter writer, int sampleRate, short channels, short bitsPerSample)
        {
            writer.Write(new[] { (byte)'R', (byte)'I', (byte)'F', (byte)'F' });
            writer.Write(0); // placeholder for file size
            writer.Write(new[] { (byte)'W', (byte)'A', (byte)'V', (byte)'E' });
            writer.Write(new[] { (byte)'f', (byte)'m', (byte)'t', (byte)' ' });
            writer.Write(16); // PCM chunk size
            writer.Write((short)1); // PCM format
            writer.Write(channels);
            writer.Write(sampleRate);
            int byteRate = sampleRate * channels * bitsPerSample / 8;
            writer.Write(byteRate);
            short blockAlign = (short)(channels * bitsPerSample / 8);
            writer.Write(blockAlign);
            writer.Write(bitsPerSample);
            writer.Write(new[] { (byte)'d', (byte)'a', (byte)'t', (byte)'a' });
            writer.Write(0); // placeholder for data length
        }

        private static void FinalizeWavHeader(BinaryWriter writer, long dataLength)
        {
            long fileLength = 36 + dataLength;
            writer.Seek(4, SeekOrigin.Begin);
            writer.Write((int)fileLength);
            writer.Seek(40, SeekOrigin.Begin);
            writer.Write((int)dataLength);
            writer.Seek(0, SeekOrigin.End);
        }
    }
}

